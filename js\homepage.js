// Homepage Logic for Quiz Categories
class Homepage {
    constructor() {
        this.categories = [];
        this.filteredCategories = [];
        this.currentUser = this.getCurrentUser();
        this.csvEngine = new CSVQuizEngine();
        this.testFileManager = new TestFileManager();
        this.selectedTestFile = null;

        this.initializeEventListeners();
        this.loadData();
        this.loadAvailableTests();
    }

    initializeEventListeners() {
        // Search functionality
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.filterCategories(e.target.value);
        });

        // Difficulty filter
        document.getElementById('difficulty-filter').addEventListener('change', (e) => {
            this.filterByDifficulty(e.target.value);
        });

        // Action buttons
        document.getElementById('random-quiz-btn').addEventListener('click', () => {
            this.startRandomQuiz();
        });

        document.getElementById('create-quiz-btn').addEventListener('click', () => {
            this.createCustomQuiz();
        });

        // Admin panel access moved to discrete link

        document.getElementById('clear-search-btn').addEventListener('click', () => {
            this.clearSearch();
        });

        // User section buttons
        document.getElementById('view-progress-btn')?.addEventListener('click', () => {
            window.location.href = 'user-progress.html';
        });

        document.getElementById('logout-btn')?.addEventListener('click', () => {
            this.logout();
        });
    }

    async loadData() {
        try {
            // Wait for data manager to initialize
            if (dataManager.db) {
                await this.loadCategories();
                await this.loadStatistics();
                this.updateUserSection();
            } else {
                // Fallback to CSV data
                await this.loadCategoriesFromCSV();
            }
        } catch (error) {
            console.error('Error loading homepage data:', error);
            this.showError('Failed to load quiz data. Please refresh the page.');
        }
    }

    async loadCategories() {
        try {
            // Load categories from database
            const dbCategories = await dataManager.getAllCategories();
            
            // If no categories in DB, create default ones from CSV
            if (dbCategories.length === 0) {
                await this.createDefaultCategories();
                this.categories = await dataManager.getAllCategories();
            } else {
                this.categories = dbCategories;
            }

            // Add question counts to categories
            for (let category of this.categories) {
                const questions = await dataManager.getQuestionsByCategory(category.name);
                category.questionCount = questions.length;
                category.difficulties = this.getUniqueDifficulties(questions);
            }

            this.filteredCategories = [...this.categories];
            this.renderCategories();
        } catch (error) {
            console.error('Error loading categories:', error);
            await this.loadCategoriesFromCSV();
        }
    }

    async loadCategoriesFromCSV() {
        try {
            await this.csvEngine.loadQuestions();
            const questions = this.csvEngine.questions;
            
            // Group questions by category
            const categoryMap = {};
            questions.forEach(q => {
                const category = q.category || 'General';
                if (!categoryMap[category]) {
                    categoryMap[category] = {
                        name: category,
                        questions: [],
                        difficulties: new Set()
                    };
                }
                categoryMap[category].questions.push(q);
                categoryMap[category].difficulties.add(parseInt(q.difficulty));
            });

            // Convert to categories array
            this.categories = Object.values(categoryMap).map(cat => ({
                id: cat.name.toLowerCase().replace(/\s+/g, '-'),
                name: cat.name,
                description: this.getCategoryDescription(cat.name),
                icon: this.getCategoryIcon(cat.name),
                questionCount: cat.questions.length,
                difficulties: Array.from(cat.difficulties).sort(),
                averageDifficulty: this.calculateAverageDifficulty(cat.questions)
            }));

            this.filteredCategories = [...this.categories];
            this.renderCategories();
        } catch (error) {
            console.error('Error loading categories from CSV:', error);
            this.showError('Failed to load quiz categories.');
        }
    }

    async createDefaultCategories() {
        const defaultCategories = [
            {
                name: 'Mathematics',
                description: 'Test your mathematical skills from basic arithmetic to advanced calculus',
                icon: '🔢',
                color: '#4CAF50'
            },
            {
                name: 'Science',
                description: 'Explore physics, chemistry, biology, and general science concepts',
                icon: '🔬',
                color: '#2196F3'
            },
            {
                name: 'Geography',
                description: 'Discover world geography, capitals, countries, and landmarks',
                icon: '🌍',
                color: '#FF9800'
            },
            {
                name: 'History',
                description: 'Journey through historical events, dates, and important figures',
                icon: '📚',
                color: '#9C27B0'
            },
            {
                name: 'Literature',
                description: 'Test your knowledge of classic and modern literature',
                icon: '📖',
                color: '#E91E63'
            },
            {
                name: 'General Knowledge',
                description: 'Mixed topics covering various subjects and current affairs',
                icon: '🧠',
                color: '#607D8B'
            }
        ];

        for (let category of defaultCategories) {
            try {
                await dataManager.saveCategory(category);
            } catch (error) {
                console.log(`Category ${category.name} might already exist`);
            }
        }
    }

    async loadStatistics() {
        try {
            const analytics = await dataManager.getAnalytics();
            
            document.getElementById('total-categories').textContent = analytics.totalCategories;
            document.getElementById('total-questions').textContent = 
                Object.values(analytics.categoryStats).reduce((sum, cat) => sum + cat.totalAttempts, 0);
            document.getElementById('total-users').textContent = analytics.totalUsers;
            document.getElementById('avg-score').textContent = 
                analytics.averageTScore ? `${Math.round(analytics.averageTScore)}` : '0';
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateUserSection() {
        const userSection = document.getElementById('user-section');
        const welcomeMessage = document.getElementById('welcome-message');
        
        if (this.currentUser) {
            userSection.style.display = 'block';
            welcomeMessage.textContent = `Welcome back, ${this.currentUser}!`;
            this.loadUserStats();
        } else {
            userSection.style.display = 'none';
        }
    }

    async loadUserStats() {
        try {
            const user = await dataManager.getUser(this.currentUser);
            const results = await dataManager.getUserResults(this.currentUser);
            
            if (user) {
                document.getElementById('user-best-score').textContent = user.bestTScore || '--';
                document.getElementById('user-total-quizzes').textContent = user.totalAttempts || 0;
                document.getElementById('user-last-quiz').textContent = 
                    user.lastQuizDate ? new Date(user.lastQuizDate).toLocaleDateString() : '--';
            }
        } catch (error) {
            console.error('Error loading user stats:', error);
        }
    }

    renderCategories() {
        const container = document.getElementById('categories-grid');
        const noCategories = document.getElementById('no-categories');
        
        if (this.filteredCategories.length === 0) {
            container.style.display = 'none';
            noCategories.style.display = 'block';
            return;
        }
        
        container.style.display = 'grid';
        noCategories.style.display = 'none';
        
        container.innerHTML = this.filteredCategories.map(category => `
            <div class="category-card" onclick="homepage.startCategoryQuiz('${category.name}')">
                <span class="category-icon">${category.icon || '📚'}</span>
                <h3 class="category-title">${category.name}</h3>
                <p class="category-description">${category.description || 'Test your knowledge in this category'}</p>
                
                <div class="category-stats">
                    <span>${category.questionCount || 0} Questions</span>
                    <span>Avg Difficulty: ${this.getDifficultyName(category.averageDifficulty)}</span>
                </div>
                
                <div class="category-difficulty">
                    ${[1,2,3,4,5].map(level => `
                        <div class="difficulty-dot ${(category.difficulties || []).includes(level) ? 'active' : ''}"></div>
                    `).join('')}
                </div>
                
                <button class="btn btn-primary" style="width: 100%;">Start Quiz</button>
            </div>
        `).join('');
    }

    filterCategories(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredCategories = this.categories.filter(category =>
            category.name.toLowerCase().includes(term) ||
            (category.description || '').toLowerCase().includes(term)
        );
        this.renderCategories();
    }

    filterByDifficulty(difficulty) {
        if (!difficulty) {
            this.filteredCategories = [...this.categories];
        } else {
            const diffLevel = parseInt(difficulty);
            this.filteredCategories = this.categories.filter(category =>
                (category.difficulties || []).includes(diffLevel)
            );
        }
        this.renderCategories();
    }

    clearSearch() {
        document.getElementById('search-input').value = '';
        document.getElementById('difficulty-filter').value = '';
        this.filteredCategories = [...this.categories];
        this.renderCategories();
    }

    async startCategoryQuiz(categoryName) {
        try {
            // Store selected category and question count for the quiz
            const questionCount = document.getElementById('question-count-filter').value;
            const testFileId = this.selectedTestFile || 'default';

            localStorage.setItem('selectedCategory', categoryName);
            localStorage.setItem('quizMode', 'category');
            localStorage.setItem('questionQuantity', questionCount);
            localStorage.setItem('selectedTestFile', testFileId);

            // Redirect to quiz page
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Error starting category quiz:', error);
            alert('Failed to start quiz. Please try again.');
        }
    }

    async startRandomQuiz() {
        try {
            const questionCount = document.getElementById('question-count-filter').value;
            const testFileId = this.selectedTestFile || 'default';

            localStorage.setItem('quizMode', 'random');
            localStorage.setItem('questionQuantity', questionCount);
            localStorage.setItem('selectedTestFile', testFileId);
            localStorage.removeItem('selectedCategory');
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Error starting random quiz:', error);
            alert('Failed to start quiz. Please try again.');
        }
    }

    createCustomQuiz() {
        // Redirect to custom quiz creator (to be implemented)
        alert('Custom quiz creator coming soon!');
    }

    getCurrentUser() {
        return localStorage.getItem('currentUser') || null;
    }

    logout() {
        localStorage.removeItem('currentUser');
        this.currentUser = null;
        this.updateUserSection();
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1000;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 5000);
    }

    // Utility methods
    getCategoryDescription(name) {
        const descriptions = {
            'Mathematics': 'Test your mathematical skills from basic arithmetic to advanced calculus',
            'Science': 'Explore physics, chemistry, biology, and general science concepts',
            'Geography': 'Discover world geography, capitals, countries, and landmarks',
            'History': 'Journey through historical events, dates, and important figures',
            'Literature': 'Test your knowledge of classic and modern literature',
            'Art': 'Explore art history, famous artists, and artistic movements',
            'Music': 'Test your knowledge of music theory, composers, and musical history',
            'Philosophy': 'Dive into philosophical concepts, thinkers, and schools of thought',
            'Physics': 'Challenge yourself with physics concepts and principles',
            'Language': 'Test your language skills and grammar knowledge'
        };
        return descriptions[name] || 'Test your knowledge in this category';
    }

    getCategoryIcon(name) {
        const icons = {
            'Mathematics': '🔢', 'Math': '🔢',
            'Science': '🔬',
            'Geography': '🌍',
            'History': '📚',
            'Literature': '📖',
            'Art': '🎨',
            'Music': '🎵',
            'Philosophy': '🤔',
            'Physics': '⚛️',
            'Language': '🗣️',
            'General Knowledge': '🧠'
        };
        return icons[name] || '📚';
    }

    getUniqueDifficulties(questions) {
        return [...new Set(questions.map(q => parseInt(q.difficulty)))].sort();
    }

    calculateAverageDifficulty(questions) {
        if (questions.length === 0) return 3;
        const sum = questions.reduce((acc, q) => acc + parseInt(q.difficulty), 0);
        return Math.round(sum / questions.length);
    }

    getDifficultyName(level) {
        const names = {
            1: 'Very Easy',
            2: 'Easy',
            3: 'Medium',
            4: 'Hard',
            5: 'Very Hard'
        };
        return names[level] || 'Medium';
    }

    // Test File Management Methods
    loadAvailableTests() {
        try {
            const activeTests = this.testFileManager.getActiveTestFiles();
            this.renderAvailableTests(activeTests);
        } catch (error) {
            console.error('Error loading available tests:', error);
        }
    }

    renderAvailableTests(tests) {
        const container = document.getElementById('available-tests');
        if (!container) return;

        container.innerHTML = '';

        if (tests.length === 0) {
            container.innerHTML = `
                <div class="no-tests-message">
                    <div class="no-tests-icon">📁</div>
                    <h3>No Test Files Available</h3>
                    <p>Contact your administrator to add test files.</p>
                </div>
            `;
            return;
        }

        tests.forEach(test => {
            const testCard = this.createTestFileCard(test);
            container.appendChild(testCard);
        });
    }

    createTestFileCard(test) {
        const card = document.createElement('div');
        card.className = 'test-file-option';
        card.dataset.testId = test.id;

        const icon = this.getTestFileIcon(test.type);
        const difficultyText = test.difficulty === 'mixed' ? 'Mixed' : `Level ${test.difficulty}`;

        card.innerHTML = `
            <div class="test-file-icon">${icon}</div>
            <div class="test-file-name">${test.name}</div>
            <div class="test-file-description">${test.description}</div>
            <div class="test-file-stats">
                <span>
                    <span class="stat-value">${test.questionCount}</span>
                    <span>Questions</span>
                </span>
                <span>
                    <span class="stat-value">${difficultyText}</span>
                    <span>Difficulty</span>
                </span>
                <span>
                    <span class="stat-value">${test.categories.length}</span>
                    <span>Categories</span>
                </span>
            </div>
        `;

        card.addEventListener('click', () => {
            this.selectTestFile(test.id);
        });

        return card;
    }

    getTestFileIcon(type) {
        const icons = {
            'multiple-choice': '📝',
            'pretest': '📋',
            'mixed': '📚',
            'assessment': '🎯',
            'practice': '💪',
            'exam': '🏆'
        };
        return icons[type] || '📄';
    }

    selectTestFile(testId) {
        // Remove previous selection
        document.querySelectorAll('.test-file-option').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked card
        const selectedCard = document.querySelector(`[data-test-id="${testId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        this.selectedTestFile = testId;

        // Store selection for quiz
        localStorage.setItem('selectedTestFile', testId);

        // Update categories based on selected test
        this.loadCategoriesForTest(testId);

        // Show notification
        const testFile = this.testFileManager.getTestFile(testId);
        if (testFile) {
            this.showNotification(`Selected: ${testFile.name}`, 'success');
        }
    }

    async loadCategoriesForTest(testId) {
        try {
            const questions = await this.testFileManager.loadQuestionsForTest(testId);
            const categories = [...new Set(questions.map(q => q.category).filter(Boolean))];

            // Update categories display
            this.categories = categories.map(cat => ({
                name: cat,
                questionCount: questions.filter(q => q.category === cat).length,
                difficulties: this.getUniqueDifficulties(questions.filter(q => q.category === cat)),
                averageDifficulty: this.calculateAverageDifficulty(questions.filter(q => q.category === cat)),
                description: this.getCategoryDescription(cat),
                icon: this.getCategoryIcon(cat)
            }));

            this.filteredCategories = [...this.categories];
            this.renderCategories();

        } catch (error) {
            console.error('Error loading categories for test:', error);
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        // Set background color based on type
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196F3'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize homepage when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.homepage = new Homepage();
});

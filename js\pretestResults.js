// Pre-test Results Page Logic
function initializePretestResults() {
    // Get results from localStorage
    const resultsData = localStorage.getItem('quizResults');
    
    if (!resultsData) {
        // No results found, redirect to pre-test
        alert('No pre-test results found. Please take the pre-test first.');
        window.location.href = 'pretest.html';
        return;
    }
    
    const results = JSON.parse(resultsData);
    
    // Verify this is a pre-test result
    if (results.testType !== 'pretest') {
        alert('Invalid pre-test results. Please take the pre-test first.');
        window.location.href = 'pretest.html';
        return;
    }
    
    displayPretestResults(results);
    setupEventListeners(results);
}

function displayPretestResults(results) {
    // Update result status
    updateResultStatus(results);
    
    // Update statistics
    updateStatistics(results);
    
    // Create charts
    createTypePerformanceChart(results);
    createCategoryPerformanceChart(results);
    
    // Display question breakdown
    displayQuestionBreakdown(results);
    
    // Generate recommendations
    generateRecommendations(results);
    
    // Update next steps
    updateNextSteps(results);
}

function updateResultStatus(results) {
    const statusElement = document.getElementById('result-status');
    const iconElement = document.getElementById('status-icon');
    const titleElement = document.getElementById('status-title');
    const scoreElement = document.getElementById('score-display');
    const subtitleElement = document.getElementById('status-subtitle');
    
    if (results.passed) {
        statusElement.className = 'result-status status-passed';
        iconElement.textContent = '🎉';
        titleElement.textContent = 'Pre-test Passed!';
        subtitleElement.textContent = `Congratulations ${results.username}! You're ready for the main quiz.`;
    } else {
        statusElement.className = 'result-status status-failed';
        iconElement.textContent = '📚';
        titleElement.textContent = 'Pre-test Not Passed';
        subtitleElement.textContent = `Don't worry ${results.username}! Review the areas below and try again.`;
    }
    
    scoreElement.textContent = `${results.rawScore}%`;
}

function updateStatistics(results) {
    document.getElementById('correct-answers').textContent = results.correctAnswers;
    document.getElementById('incorrect-answers').textContent = results.incorrectAnswers;
    document.getElementById('total-time').textContent = formatTime(results.totalTime);
    document.getElementById('passing-threshold').textContent = `${results.passingThreshold}%`;
}

function createTypePerformanceChart(results) {
    const ctx = document.getElementById('type-performance-chart').getContext('2d');
    
    const typeStats = results.typeStats || {};
    const types = Object.keys(typeStats);
    const accuracies = types.map(type => {
        const stats = typeStats[type];
        return stats.total > 0 ? (stats.correct / stats.total) * 100 : 0;
    });
    
    const typeNames = {
        'fill-blank': 'Fill in the Blank',
        'yes-no': 'Yes / No'
    };
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: types.map(type => typeNames[type] || type),
            datasets: [{
                data: accuracies,
                backgroundColor: [
                    '#4CAF50',
                    '#FF9800'
                ],
                borderColor: [
                    '#388E3C',
                    '#F57C00'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const type = types[context.dataIndex];
                            const stats = typeStats[type];
                            return [
                                `${context.label}: ${context.parsed.toFixed(1)}%`,
                                `Correct: ${stats.correct}/${stats.total}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function createCategoryPerformanceChart(results) {
    const ctx = document.getElementById('category-performance-chart').getContext('2d');
    
    const categoryStats = results.categoryStats || {};
    const categories = Object.keys(categoryStats);
    const accuracies = categories.map(category => {
        const stats = categoryStats[category];
        return stats.total > 0 ? (stats.correct / stats.total) * 100 : 0;
    });
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: categories,
            datasets: [{
                label: 'Accuracy (%)',
                data: accuracies,
                backgroundColor: categories.map((_, i) => 
                    `hsla(${(i * 360 / categories.length)}, 70%, 60%, 0.8)`
                ),
                borderColor: categories.map((_, i) => 
                    `hsla(${(i * 360 / categories.length)}, 70%, 50%, 1)`
                ),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Accuracy (%)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const category = categories[context.dataIndex];
                            const stats = categoryStats[category];
                            return [
                                `Accuracy: ${context.parsed.y.toFixed(1)}%`,
                                `Correct: ${stats.correct}/${stats.total}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function displayQuestionBreakdown(results) {
    const container = document.getElementById('question-breakdown-list');
    
    if (!results.answers || results.answers.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666;">No question details available.</p>';
        return;
    }
    
    const breakdownHTML = results.answers.map((answer, index) => {
        const statusClass = answer.isCorrect ? 'status-correct' : 'status-incorrect';
        const statusIcon = answer.isCorrect ? '✓' : '✗';
        const statusText = answer.isCorrect ? 'Correct' : 'Incorrect';
        
        return `
            <div class="breakdown-item">
                <div class="question-info">
                    <div class="question-text">Q${index + 1}: ${answer.question}</div>
                    <div class="question-meta">
                        Type: ${answer.type === 'fill-blank' ? 'Fill in the Blank' : 'Yes/No'} • 
                        Category: ${answer.category} • 
                        Time: ${((answer.timeTaken || 0) / 1000).toFixed(1)}s
                    </div>
                </div>
                <div class="answer-status">
                    <div class="${statusClass}">${statusIcon} ${statusText}</div>
                    <div class="user-answer">Your answer: "${answer.userAnswer}"</div>
                    ${!answer.isCorrect ? `<div class="user-answer">Correct: "${answer.correctAnswer}"</div>` : ''}
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = breakdownHTML;
}

function generateRecommendations(results) {
    const container = document.getElementById('recommendations-content');
    const recommendations = [];
    
    // Performance-based recommendations
    if (results.passed) {
        recommendations.push({
            title: "🎯 Ready for Main Quiz",
            description: "Excellent work! You've demonstrated good foundational knowledge. You're ready to take the main adaptive quiz which will provide more detailed T-score analysis."
        });
        
        recommendations.push({
            title: "📈 Continue Learning",
            description: "Consider exploring more challenging topics or categories where you want to improve further. The main quiz will adapt to your skill level."
        });
    } else {
        recommendations.push({
            title: "📚 Review Fundamentals",
            description: `You scored ${results.rawScore}% but need ${results.passingThreshold}% to pass. Focus on reviewing basic concepts in the areas where you struggled.`
        });
        
        recommendations.push({
            title: "🔄 Practice More",
            description: "Take some time to study the topics covered in this pre-test, then retake it. Each attempt will help you improve."
        });
    }
    
    // Type-specific recommendations
    const typeStats = results.typeStats || {};
    Object.entries(typeStats).forEach(([type, stats]) => {
        const accuracy = stats.total > 0 ? (stats.correct / stats.total) * 100 : 0;
        const typeName = type === 'fill-blank' ? 'Fill-in-the-blank' : 'Yes/No';
        
        if (accuracy < 70) {
            recommendations.push({
                title: `📝 Improve ${typeName} Questions`,
                description: `Your accuracy on ${typeName.toLowerCase()} questions was ${accuracy.toFixed(1)}%. Practice more questions of this type to improve your performance.`
            });
        }
    });
    
    // Category-specific recommendations
    const categoryStats = results.categoryStats || {};
    const weakestCategory = Object.entries(categoryStats)
        .filter(([_, stats]) => stats.total >= 2) // Only consider categories with at least 2 questions
        .sort(([_, a], [__, b]) => (a.correct / a.total) - (b.correct / b.total))[0];
    
    if (weakestCategory) {
        const [category, stats] = weakestCategory;
        const accuracy = (stats.correct / stats.total) * 100;
        if (accuracy < 70) {
            recommendations.push({
                title: `🎓 Focus on ${category}`,
                description: `Your performance in ${category} was ${accuracy.toFixed(1)}%. Consider reviewing materials in this subject area.`
            });
        }
    }
    
    const recommendationsHTML = recommendations.map(rec => `
        <div class="recommendation-item">
            <h4>${rec.title}</h4>
            <p>${rec.description}</p>
        </div>
    `).join('');
    
    container.innerHTML = recommendationsHTML;
}

function updateNextSteps(results) {
    const nextStepsText = document.getElementById('next-steps-text');
    
    if (results.passed) {
        nextStepsText.textContent = "Great job! You've passed the pre-test and are ready for the main adaptive quiz. This will provide detailed T-score analysis and track your progress over time.";
    } else {
        nextStepsText.textContent = "Don't worry! Use this as a learning opportunity. Review the areas where you struggled, then retake the pre-test when you feel ready.";
    }
}

function setupEventListeners(results) {
    // Take main quiz button
    document.getElementById('take-main-quiz').addEventListener('click', () => {
        if (results.passed) {
            window.location.href = 'index.html';
        } else {
            if (confirm('You haven\'t passed the pre-test yet. Are you sure you want to take the main quiz?')) {
                window.location.href = 'index.html';
            }
        }
    });
    
    // Retake pre-test button
    document.getElementById('retake-pretest').addEventListener('click', () => {
        localStorage.removeItem('quizResults');
        window.location.href = 'pretest.html';
    });
    
    // View progress button
    document.getElementById('view-progress').addEventListener('click', () => {
        window.location.href = 'user-progress.html';
    });
}

function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}

// Initialize results page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePretestResults();
});

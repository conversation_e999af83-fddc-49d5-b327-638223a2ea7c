// Test File Manager - Handles multiple CSV test files
class TestFileManager {
    constructor() {
        this.testFiles = new Map();
        this.activeTestFile = 'default';
        this.loadTestFiles();
    }

    // Load all test files from localStorage
    loadTestFiles() {
        const savedFiles = localStorage.getItem('testFiles');
        if (savedFiles) {
            const files = JSON.parse(savedFiles);
            Object.entries(files).forEach(([id, data]) => {
                this.testFiles.set(id, data);
            });
        } else {
            // Initialize with default test files
            this.initializeDefaultFiles();
        }
    }

    // Initialize default test files
    initializeDefaultFiles() {
        const defaultTest = {
            id: 'default',
            name: 'Default Question Set',
            description: 'General knowledge questions across multiple subjects',
            category: 'General',
            type: 'multiple-choice',
            difficulty: 'mixed',
            tags: ['general', 'mixed', 'knowledge'],
            active: true,
            fileName: 'questions.csv',
            questionCount: 40,
            categories: ['Math', 'Science', 'History', 'Geography'],
            created: new Date().toISOString(),
            modified: new Date().toISOString()
        };

        const pretestFile = {
            id: 'pretest',
            name: 'Pre-test Assessment',
            description: 'Knowledge evaluation with fill-in-blank and yes/no questions',
            category: 'Assessment',
            type: 'pretest',
            difficulty: 'mixed',
            tags: ['pretest', 'assessment', 'evaluation'],
            active: true,
            fileName: 'pretest-questions.csv',
            questionCount: 36,
            categories: ['Math', 'Science', 'General'],
            created: new Date().toISOString(),
            modified: new Date().toISOString()
        };

        this.testFiles.set('default', defaultTest);
        this.testFiles.set('pretest', pretestFile);
        this.saveTestFiles();
    }

    // Save test files to localStorage
    saveTestFiles() {
        const filesObject = {};
        this.testFiles.forEach((data, id) => {
            filesObject[id] = data;
        });
        localStorage.setItem('testFiles', JSON.stringify(filesObject));
    }

    // Create a new test file
    createTestFile(testData) {
        const testFile = {
            id: testData.id,
            name: testData.name,
            description: testData.description || '',
            category: testData.category,
            type: testData.type || 'multiple-choice',
            difficulty: testData.difficulty || 'mixed',
            tags: testData.tags ? testData.tags.split(',').map(t => t.trim()) : [],
            active: testData.active || false,
            fileName: `${testData.id}.csv`,
            questionCount: 0,
            categories: [],
            questions: [],
            created: new Date().toISOString(),
            modified: new Date().toISOString()
        };

        this.testFiles.set(testData.id, testFile);
        this.saveTestFiles();
        return testFile;
    }

    // Get test file by ID
    getTestFile(id) {
        return this.testFiles.get(id);
    }

    // Get all test files
    getAllTestFiles() {
        return Array.from(this.testFiles.values());
    }

    // Get active test files
    getActiveTestFiles() {
        return Array.from(this.testFiles.values()).filter(file => file.active);
    }

    // Update test file
    updateTestFile(id, updates) {
        const testFile = this.testFiles.get(id);
        if (testFile) {
            Object.assign(testFile, updates, {
                modified: new Date().toISOString()
            });
            this.saveTestFiles();
            return testFile;
        }
        return null;
    }

    // Delete test file
    deleteTestFile(id) {
        if (id === 'default' || id === 'pretest') {
            throw new Error('Cannot delete default test files');
        }
        
        const deleted = this.testFiles.delete(id);
        if (deleted) {
            this.saveTestFiles();
            // Remove from localStorage
            localStorage.removeItem(`testQuestions_${id}`);
        }
        return deleted;
    }

    // Duplicate test file
    duplicateTestFile(sourceId, newName) {
        const sourceFile = this.testFiles.get(sourceId);
        if (!sourceFile) {
            throw new Error('Source test file not found');
        }

        const newId = newName.toLowerCase().replace(/[^a-z0-9]/g, '-');
        const duplicatedFile = {
            ...sourceFile,
            id: newId,
            name: newName,
            fileName: `${newId}.csv`,
            active: false,
            created: new Date().toISOString(),
            modified: new Date().toISOString()
        };

        this.testFiles.set(newId, duplicatedFile);
        
        // Copy questions if they exist
        const sourceQuestions = localStorage.getItem(`testQuestions_${sourceId}`);
        if (sourceQuestions) {
            localStorage.setItem(`testQuestions_${newId}`, sourceQuestions);
        }

        this.saveTestFiles();
        return duplicatedFile;
    }

    // Set active test file
    setActiveTestFile(id) {
        if (this.testFiles.has(id)) {
            this.activeTestFile = id;
            localStorage.setItem('activeTestFile', id);
            return true;
        }
        return false;
    }

    // Get active test file
    getActiveTestFile() {
        const saved = localStorage.getItem('activeTestFile');
        if (saved && this.testFiles.has(saved)) {
            this.activeTestFile = saved;
        }
        return this.getTestFile(this.activeTestFile);
    }

    // Load questions for a test file
    async loadQuestionsForTest(testId) {
        const testFile = this.getTestFile(testId);
        if (!testFile) {
            throw new Error('Test file not found');
        }

        // Try to load from localStorage first
        const savedQuestions = localStorage.getItem(`testQuestions_${testId}`);
        if (savedQuestions) {
            return JSON.parse(savedQuestions);
        }

        // Try to load from CSV file
        try {
            const response = await fetch(`data/${testFile.fileName}`);
            if (response.ok) {
                const csvText = await response.text();
                const questions = this.parseCSV(csvText);
                
                // Save to localStorage for future use
                localStorage.setItem(`testQuestions_${testId}`, JSON.stringify(questions));
                
                // Update test file metadata
                this.updateTestFileMetadata(testId, questions);
                
                return questions;
            }
        } catch (error) {
            console.warn(`Could not load CSV file for test ${testId}:`, error);
        }

        // Return empty array if no questions found
        return [];
    }

    // Save questions for a test file
    saveQuestionsForTest(testId, questions) {
        localStorage.setItem(`testQuestions_${testId}`, JSON.stringify(questions));
        this.updateTestFileMetadata(testId, questions);
    }

    // Update test file metadata based on questions
    updateTestFileMetadata(testId, questions) {
        const testFile = this.getTestFile(testId);
        if (testFile && questions) {
            const categories = [...new Set(questions.map(q => q.category).filter(Boolean))];
            this.updateTestFile(testId, {
                questionCount: questions.length,
                categories: categories
            });
        }
    }

    // Parse CSV content
    parseCSV(csvText) {
        const lines = csvText.trim().split('\n');
        const questions = [];

        for (let i = 1; i < lines.length; i++) { // Skip header
            const line = lines[i].trim();
            if (!line) continue;

            try {
                const columns = this.parseCSVLine(line);
                if (columns.length >= 9) {
                    const question = {
                        id: columns[0],
                        question: columns[1],
                        choices: {
                            A: columns[2],
                            B: columns[3],
                            C: columns[4],
                            D: columns[5]
                        },
                        correctAnswer: columns[6],
                        hint: columns[7],
                        difficulty: parseInt(columns[8]) || 1,
                        category: columns[9] || 'General'
                    };
                    questions.push(question);
                }
            } catch (error) {
                console.warn(`Error parsing CSV line ${i + 1}:`, error);
            }
        }

        return questions;
    }

    // Parse a single CSV line (handles quoted fields)
    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current.trim());
        return result;
    }

    // Generate CSV content from questions
    generateCSV(questions) {
        const header = 'id,question,choice_a,choice_b,choice_c,choice_d,correct_answer,hint,difficulty,category';
        const rows = questions.map(q => {
            const row = [
                q.id,
                `"${q.question.replace(/"/g, '""')}"`,
                `"${q.choices.A.replace(/"/g, '""')}"`,
                `"${q.choices.B.replace(/"/g, '""')}"`,
                `"${q.choices.C.replace(/"/g, '""')}"`,
                `"${q.choices.D.replace(/"/g, '""')}"`,
                q.correctAnswer,
                `"${(q.hint || '').replace(/"/g, '""')}"`,
                q.difficulty,
                q.category
            ];
            return row.join(',');
        });
        
        return [header, ...rows].join('\n');
    }

    // Export test file as CSV
    exportTestFile(testId) {
        const testFile = this.getTestFile(testId);
        const questions = JSON.parse(localStorage.getItem(`testQuestions_${testId}`) || '[]');
        
        if (questions.length === 0) {
            throw new Error('No questions to export');
        }

        const csvContent = this.generateCSV(questions);
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = testFile.fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Get statistics for all test files
    getStatistics() {
        const files = this.getAllTestFiles();
        const totalQuestions = files.reduce((sum, file) => sum + file.questionCount, 0);
        const activeTests = files.filter(file => file.active).length;
        const allCategories = new Set();
        
        files.forEach(file => {
            file.categories.forEach(cat => allCategories.add(cat));
        });

        return {
            totalFiles: files.length,
            totalQuestions,
            activeTests,
            totalCategories: allCategories.size,
            categories: Array.from(allCategories)
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TestFileManager;
}

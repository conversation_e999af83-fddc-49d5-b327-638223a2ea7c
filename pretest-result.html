<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pre-test Results - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .pretest-results-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .result-status {
            text-align: center;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .status-passed {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        
        .status-failed {
            background: linear-gradient(135deg, #F44336, #d32f2f);
            color: white;
        }
        
        .status-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .status-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status-subtitle {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .score-display {
            font-size: 48px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .pretest-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .pretest-stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .question-breakdown {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }
        
        .breakdown-item:hover {
            background: #f8f9fa;
        }
        
        .breakdown-item:last-child {
            border-bottom: none;
        }
        
        .question-info {
            flex: 1;
        }
        
        .question-text {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .question-meta {
            font-size: 12px;
            color: #666;
        }
        
        .answer-status {
            text-align: right;
        }
        
        .status-correct {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .status-incorrect {
            color: #F44336;
            font-weight: bold;
        }
        
        .user-answer {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .recommendations {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .recommendation-item {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
        }
        
        .recommendation-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .chart-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .next-steps {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .next-steps h3 {
            margin: 0 0 20px 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-white {
            background: white;
            color: #667eea;
            border: 2px solid white;
        }
        
        .btn-white:hover {
            background: rgba(255,255,255,0.9);
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .action-buttons .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="pretest-results-container">
        <!-- Result Status -->
        <div id="result-status" class="result-status">
            <div class="status-icon" id="status-icon">📋</div>
            <div class="status-title" id="status-title">Pre-test Complete</div>
            <div class="score-display" id="score-display">0%</div>
            <div class="status-subtitle" id="status-subtitle">Loading results...</div>
        </div>

        <!-- Statistics Grid -->
        <div class="pretest-stats-grid">
            <div class="pretest-stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-value" id="correct-answers">0</div>
                <div class="stat-label">Correct Answers</div>
            </div>
            <div class="pretest-stat-card">
                <div class="stat-icon">❌</div>
                <div class="stat-value" id="incorrect-answers">0</div>
                <div class="stat-label">Incorrect Answers</div>
            </div>
            <div class="pretest-stat-card">
                <div class="stat-icon">⏱️</div>
                <div class="stat-value" id="total-time">0s</div>
                <div class="stat-label">Total Time</div>
            </div>
            <div class="pretest-stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-value" id="passing-threshold">80%</div>
                <div class="stat-label">Passing Threshold</div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="charts-grid">
            <div class="chart-section">
                <h3>📊 Performance by Type</h3>
                <canvas id="type-performance-chart"></canvas>
            </div>
            <div class="chart-section">
                <h3>📚 Performance by Category</h3>
                <canvas id="category-performance-chart"></canvas>
            </div>
        </div>

        <!-- Question Breakdown -->
        <div class="question-breakdown">
            <h3>📝 Question-by-Question Breakdown</h3>
            <div id="question-breakdown-list">
                <!-- Questions will be loaded here -->
            </div>
        </div>

        <!-- Recommendations -->
        <div id="recommendations" class="recommendations">
            <h3>💡 Recommendations</h3>
            <div id="recommendations-content">
                <!-- Recommendations will be loaded here -->
            </div>
        </div>

        <!-- Next Steps -->
        <div class="next-steps">
            <h3>🚀 What's Next?</h3>
            <p id="next-steps-text">Based on your pre-test performance, here are your recommended next steps.</p>
            <div class="action-buttons">
                <button id="take-main-quiz" class="btn btn-white">Take Main Quiz</button>
                <button id="retake-pretest" class="btn btn-outline" style="border-color: white; color: white;">Retake Pre-test</button>
                <button id="view-progress" class="btn btn-outline" style="border-color: white; color: white;">View Progress</button>
                <a href="homepage.html" class="btn btn-outline" style="border-color: white; color: white;">Back to Home</a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/dataManager.js"></script>
    <script src="js/pretestResults.js"></script>
</body>
</html>

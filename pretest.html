<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pre-test Assessment - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">

    <!-- MathJax for LaTeX support -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .pretest-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .pretest-header {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .pretest-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .pretest-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .pretest-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .pretest-info li {
            margin: 5px 0;
            color: #424242;
        }
        
        .fill-blank-input {
            display: inline-block;
            min-width: 150px;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            background: #fff;
            margin: 0 5px;
        }
        
        .fill-blank-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .yes-no-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .yes-no-option {
            flex: 1;
            max-width: 200px;
        }
        
        .yes-no-option input[type="radio"] {
            display: none;
        }
        
        .yes-no-label {
            display: block;
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .yes-no-label:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .yes-no-option input[type="radio"]:checked + .yes-no-label {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .question-type-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 15px;
        }
        
        .type-fill-blank {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .type-yes-no {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .pretest-progress {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #45a049);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .pretest-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .answer-feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }
        
        .feedback-correct {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            color: #2e7d32;
        }
        
        .feedback-incorrect {
            background: #ffebee;
            border-left: 4px solid #f44336;
            color: #c62828;
        }
        
        .hint-section {
            background: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin-top: 15px;
            border-radius: 0 10px 10px 0;
            display: none;
        }
        
        .hint-section h4 {
            margin: 0 0 10px 0;
            color: #f57c00;
        }
    </style>
</head>
<body>
    <div class="pretest-container">
        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active">
            <div class="pretest-header">
                <h1>📋 Pre-test Assessment</h1>
                <p>Evaluate your current knowledge level before starting the main quiz</p>
            </div>

            <div class="pretest-info">
                <h3>📝 Pre-test Information</h3>
                <ul>
                    <li><strong>Question Types:</strong> Fill-in-the-blank and Yes/No questions</li>
                    <li><strong>Total Questions:</strong> 10 questions</li>
                    <li><strong>Time Limit:</strong> No time limit (take your time)</li>
                    <li><strong>Passing Score:</strong> 80% (8 out of 10 correct)</li>
                    <li><strong>Purpose:</strong> Assess your readiness for the main quiz</li>
                </ul>
                
                <div style="margin-top: 20px; padding: 15px; background: rgba(255,193,7,0.1); border-radius: 8px;">
                    <strong>💡 Tips:</strong>
                    <ul style="margin: 10px 0;">
                        <li>Read each question carefully</li>
                        <li>For fill-in-blank questions, type your answer in the blank space</li>
                        <li>For Yes/No questions, select the appropriate option</li>
                        <li>You can use hints if you need help</li>
                    </ul>
                </div>
            </div>

            <div class="form-group">
                <label for="pretest-username">Enter your name:</label>
                <input type="text" id="pretest-username" placeholder="Your name" required>
            </div>

            <div class="pretest-actions">
                <button id="start-pretest" class="btn btn-primary">Start Pre-test</button>
                <a href="homepage.html" class="btn btn-secondary">Back to Home</a>
            </div>
        </div>

        <!-- Pre-test Screen -->
        <div id="pretest-screen" class="screen">
            <div class="pretest-progress">
                <div class="progress-header">
                    <span>Question <span id="question-number">1</span> of <span id="total-questions">10</span></span>
                    <span id="current-user">User</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="question-card">
                <div class="question-type-indicator" id="question-type">Fill in the Blank</div>
                <h2 id="question-text">Loading question...</h2>
                
                <!-- Fill-in-blank container -->
                <div id="fill-blank-container" style="display: none;">
                    <div id="fill-blank-question"></div>
                </div>
                
                <!-- Yes/No container -->
                <div id="yes-no-container" class="yes-no-container" style="display: none;">
                    <div class="yes-no-option">
                        <input type="radio" id="yes-option" name="yes-no" value="Yes">
                        <label for="yes-option" class="yes-no-label">✓ Yes</label>
                    </div>
                    <div class="yes-no-option">
                        <input type="radio" id="no-option" name="yes-no" value="No">
                        <label for="no-option" class="yes-no-label">✗ No</label>
                    </div>
                </div>
                
                <div class="hint-section" id="hint-section">
                    <h4>💡 Hint</h4>
                    <p id="hint-text"></p>
                </div>
                
                <div class="answer-feedback" id="answer-feedback">
                    <p id="feedback-text"></p>
                </div>
                
                <div class="quiz-actions">
                    <button id="show-hint" class="btn btn-outline">💡 Show Hint</button>
                    <button id="submit-answer" class="btn btn-primary" disabled>Submit Answer</button>
                    <button id="next-question" class="btn btn-secondary" style="display: none;">Next Question</button>
                </div>
            </div>
        </div>

        <!-- Loading Screen -->
        <div id="loading-screen" class="screen">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Loading your pre-test questions...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/dataManager.js"></script>
    <script src="js/textProcessor.js"></script>
    <script src="js/pretestEngine.js"></script>
    <script src="js/pretest.js"></script>
</body>
</html>

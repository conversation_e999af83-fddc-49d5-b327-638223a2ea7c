# HTML5 Quiz Application with CSV Database

A modern, responsive quiz application that uses CSV files as a database. Features adaptive difficulty, T-score analysis, and comprehensive results tracking.

## Features

### 🎯 Core Functionality
- **CSV-based Question Database**: Easy to manage questions in a simple CSV format
- **Adaptive Difficulty**: Questions adapt based on user performance
- **T-Score Analysis**: Statistical scoring with population comparison
- **Hint System**: Optional hints to help users learn
- **Comprehensive Results**: Detailed performance analytics with charts

### 📊 Analytics & Visualization
- Performance breakdown by difficulty level
- Category-based statistics
- T-score visualization with Chart.js
- Difficulty progression tracking
- Response time analysis

### 🎨 User Experience
- Modern, responsive design
- Smooth animations and transitions
- Mobile-friendly interface
- Real-time feedback on answers
- Progress tracking during quiz

## File Structure

```
adaptive/
├── index.html              # Main quiz interface
├── result.html             # Results page with analytics
├── admin.html              # Admin interface (existing)
├── style.css               # Complete styling
├── data/
│   └── questions.csv       # Question database
└── js/
    ├── csvQuizEngine.js    # CSV parsing and quiz logic
    ├── main.js             # Main application logic
    ├── results.js          # Results page functionality
    ├── adaptiveEngine.js   # Adaptive difficulty engine (existing)
    ├── hint.js             # Hint management (existing)
    └── api.js              # API integration (existing)
```

## CSV Question Format

The `data/questions.csv` file should follow this format:

```csv
id,question,choiceA,choiceB,choiceC,choiceD,correctAnswer,hint,difficulty,category
1,"What is the capital of France?","London","Berlin","Paris","Madrid","C","France is known for the Eiffel Tower",1,"Geography"
2,"Which planet is known as the Red Planet?","Venus","Mars","Jupiter","Saturn","B","This planet appears reddish due to iron oxide",1,"Science"
```

### CSV Fields Explanation:
- **id**: Unique identifier for the question
- **question**: The question text (use quotes if it contains commas)
- **choiceA-D**: The four multiple choice options
- **correctAnswer**: The correct choice (A, B, C, or D)
- **hint**: Optional hint text to help users
- **difficulty**: Difficulty level (1=Very Easy, 2=Easy, 3=Medium, 4=Hard, 5=Very Hard)
- **category**: Question category for analytics

## Getting Started

### 1. Setup
1. Clone or download the project files
2. Ensure all files are in the correct directory structure
3. Place your questions in `data/questions.csv` following the format above

### 2. Running the Quiz
1. Open `index.html` in a web browser
2. Enter your name and optionally enable hint mode
3. Click "Start Quiz" to begin

### 3. Viewing Results
- Results are automatically displayed after completing the quiz
- Includes T-score analysis, difficulty breakdown, and performance charts
- Results are stored in browser localStorage

## Customization

### Adding Questions
1. Edit `data/questions.csv`
2. Add new rows following the CSV format
3. Ensure proper escaping of quotes and commas
4. Refresh the quiz to load new questions

### Modifying Difficulty Levels
- Adjust the `difficulty` field in CSV (1-5 scale)
- The adaptive engine will automatically use the new difficulty distribution

### Styling
- Modify `style.css` to change colors, fonts, and layout
- CSS variables are used for easy theme customization

## Technical Details

### CSV Parsing
- Custom CSV parser handles quoted fields and special characters
- Fallback to mock data if CSV loading fails
- Real-time validation of CSV format

### Adaptive Algorithm
- Starts at medium difficulty (level 3)
- Adjusts based on user performance
- Maintains balanced question distribution

### T-Score Calculation
- Standardized scoring with population mean of 65
- Standard deviation of 15
- Percentile ranking calculation

### Browser Compatibility
- Modern browsers with ES6+ support
- Chart.js for data visualization
- LocalStorage for results persistence

## Dependencies

- **Chart.js**: For creating interactive charts and graphs
- **Modern Browser**: ES6+ features required
- **Local Server**: Recommended for CSV file loading (due to CORS restrictions)

## Development Server

For local development, use a simple HTTP server:

```bash
# Python 3
python -m http.server 8000

# Node.js (if you have http-server installed)
npx http-server

# PHP
php -S localhost:8000
```

Then visit `http://localhost:8000` in your browser.

## Troubleshooting

### CSV Not Loading
- Ensure you're running from a web server (not file:// protocol)
- Check CSV format for syntax errors
- Verify file path is correct (`data/questions.csv`)

### Questions Not Displaying
- Check browser console for JavaScript errors
- Verify CSV format matches expected structure
- Ensure all required fields are present

### Charts Not Showing
- Verify Chart.js is loading properly
- Check for JavaScript errors in console
- Ensure canvas elements exist in HTML

## Future Enhancements

- User authentication and progress tracking
- Question difficulty auto-adjustment based on success rates
- Export results to PDF or CSV
- Multiplayer quiz modes
- Question categories filtering
- Admin interface for CSV management

## License

This project is open source and available under the MIT License.

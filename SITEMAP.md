# Site Map - Adaptive Learning Platform

## 📍 Complete Page Directory

### 🏠 **Main Pages**

#### 1. **Homepage** (`homepage.html`)
- **Purpose**: Main landing page with category browser
- **Features**:
  - Quiz category browser with search and filtering
  - User dashboard with personal statistics
  - Platform statistics overview
  - Quick actions (random quiz, pre-test, admin)
- **Navigation**: Links to all other pages
- **Status**: ✅ Fully functional

#### 2. **Navigation** (`navigation.html`)
- **Purpose**: Site map and navigation hub
- **Features**:
  - Complete overview of all available pages
  - System status indicators
  - Quick links to all major features
  - Feature descriptions and status
- **Navigation**: Central hub for all pages
- **Status**: ✅ Fully functional

### 📝 **Quiz Pages**

#### 3. **Main Quiz** (`index.html`)
- **Purpose**: Adaptive multiple-choice quiz with T-score analysis
- **Features**:
  - Adaptive difficulty adjustment
  - Random choice ordering
  - T-score calculation (45-50 default, 59-69 qualifying)
  - Hint system
  - Category-specific or random questions
- **Navigation**: Links to pre-test, homepage, results
- **Status**: ✅ Fully functional

#### 4. **Pre-test Assessment** (`pretest.html`)
- **Purpose**: Knowledge evaluation with fill-in-blank and yes/no questions
- **Features**:
  - Fill-in-the-blank questions with fuzzy matching
  - Yes/No questions
  - 80% pass threshold
  - Pass/fail qualification system
  - No time limit
- **Navigation**: Links to homepage, results
- **Status**: ✅ Fully functional

### 📊 **Results Pages**

#### 5. **Main Quiz Results** (`result.html`)
- **Purpose**: Comprehensive analysis of quiz performance
- **Features**:
  - T-score visualization with gauge chart
  - Difficulty journey analysis
  - Category performance breakdown
  - Response time analysis
  - Performance radar chart
  - Population comparison
  - Historical progress tracking
  - Detailed question-by-question breakdown
- **Navigation**: Links to retake, progress, homepage
- **Status**: ✅ Fully functional with enhanced charts

#### 6. **Pre-test Results** (`pretest-result.html`)
- **Purpose**: Pre-test performance analysis and recommendations
- **Features**:
  - Pass/fail status with visual indicators
  - Performance by question type (fill-blank vs yes/no)
  - Category breakdown
  - Question-by-question analysis
  - Personalized recommendations
  - Next steps guidance
- **Navigation**: Links to main quiz, retake, progress, homepage
- **Status**: ✅ Fully functional

### 📈 **User Management Pages**

#### 7. **User Progress** (`user-progress.html`)
- **Purpose**: Personal learning analytics and progress tracking
- **Features**:
  - T-score progression over time
  - Achievement system with badges
  - Category performance analysis
  - Consistency scoring
  - Personal recommendations
  - Quiz history with detailed stats
  - Performance trends and insights
- **Navigation**: Links to quiz, pre-test, categories, homepage
- **Status**: ✅ Fully functional

### ⚙️ **Administrative Pages**

#### 8. **Admin Dashboard** (`admin.html`)
- **Purpose**: Comprehensive system administration
- **Features**:
  - User monitoring and management
  - CSV upload with validation and preview
  - Question database management
  - System settings and configuration
  - T-score parameter adjustment
  - Data export and backup
  - Analytics and reporting
- **Navigation**: Links to homepage, navigation
- **Status**: ✅ Fully functional
- **Access**: admin/admin123

## 🗂️ **Data Files**

### Question Databases
- `data/questions.csv` - Main quiz questions (30 questions, multiple categories)
- `data/pretest-questions.csv` - Pre-test questions (30 fill-blank and yes/no questions)

### JavaScript Modules
- `js/dataManager.js` - IndexedDB data management
- `js/csvQuizEngine.js` - Main quiz engine with random choice ordering
- `js/pretestEngine.js` - Pre-test engine with fuzzy matching
- `js/main.js` - Main quiz application logic
- `js/pretest.js` - Pre-test application logic
- `js/results.js` - Enhanced results with comprehensive charts
- `js/pretestResults.js` - Pre-test results analysis
- `js/homepage.js` - Homepage and category management
- `js/userProgress.js` - User progress tracking and analytics
- `js/adminDashboard.js` - Admin functionality and CSV management
- `js/adaptiveEngine.js` - Adaptive difficulty algorithm
- `js/hint.js` - Hint system management
- `js/api.js` - API integration utilities

## 🔗 **Navigation Flow**

### Primary User Journey
1. **Start**: `homepage.html` (browse categories)
2. **Assessment**: `pretest.html` (evaluate knowledge)
3. **Results**: `pretest-result.html` (see pre-test results)
4. **Main Quiz**: `index.html` (take adaptive quiz)
5. **Analysis**: `result.html` (comprehensive results)
6. **Progress**: `user-progress.html` (track improvement)

### Alternative Flows
- **Quick Start**: `index.html` → `result.html`
- **Admin**: `admin.html` (manage system)
- **Navigation**: `navigation.html` (site overview)

## ✅ **Link Verification Status**

### Internal Links
- ✅ All homepage links functional
- ✅ All quiz page navigation working
- ✅ All result page links operational
- ✅ All admin page navigation functional
- ✅ All user progress links working
- ✅ Cross-page navigation complete

### External Dependencies
- ✅ Chart.js CDN (for data visualization)
- ✅ CSS styling (responsive design)
- ✅ CSV data files (question databases)

## 🎯 **Key Features Summary**

### Quiz System
- ✅ Adaptive difficulty with T-score analysis
- ✅ Random choice ordering to prevent memorization
- ✅ Fill-in-blank and yes/no pre-test questions
- ✅ Comprehensive result analytics with multiple chart types
- ✅ Category-based organization and filtering

### User Experience
- ✅ Responsive design for all devices
- ✅ Intuitive navigation between all pages
- ✅ Progress tracking and achievement system
- ✅ Personalized recommendations
- ✅ Multiple quiz modes (category, random, pre-test)

### Administration
- ✅ CSV upload with validation and preview
- ✅ User monitoring and analytics
- ✅ System configuration and settings
- ✅ Data export and backup capabilities
- ✅ Real-time statistics and reporting

### Data Management
- ✅ IndexedDB for persistent storage
- ✅ Dynamic T-score calculation
- ✅ Historical progress tracking
- ✅ Category and difficulty analytics
- ✅ Comprehensive user profiles

## 🚀 **Getting Started**

1. **For Users**: Start at `homepage.html` or `navigation.html`
2. **For Assessment**: Begin with `pretest.html`
3. **For Quick Quiz**: Go directly to `index.html`
4. **For Administration**: Access `admin.html` (admin/admin123)
5. **For Overview**: Visit `navigation.html` for complete site map

All pages are fully functional with proper navigation and no broken links!

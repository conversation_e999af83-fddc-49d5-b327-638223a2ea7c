// API Configuration and Functions
class AdaptiveAPI {
    constructor() {
        // Google Apps Script Web App URL - Replace with your deployed script URL
        this.baseURL = 'https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec';
        this.cache = new Map();
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
    }

    // Generic API request handler with retry logic
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}?action=${endpoint}`;
        const requestOptions = {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        if (options.data) {
            requestOptions.body = JSON.stringify(options.data);
        }

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, requestOptions);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                return data;
            } catch (error) {
                console.error(`API request attempt ${attempt} failed:`, error);
                
                if (attempt === this.retryAttempts) {
                    throw error;
                }
                
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
            }
        }
    }

    // Get questions by difficulty level
    async getQuestions(level = 3, count = 1) {
        const cacheKey = `questions_${level}_${count}`;
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const data = await this.makeRequest('getQuestions', {
                method: 'GET',
                data: { level, count }
            });

            // Cache the result for 5 minutes
            this.cache.set(cacheKey, data);
            setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);

            return data;
        } catch (error) {
            console.error('Error fetching questions:', error);
            // Return mock data for development
            return this.getMockQuestions(level, count);
        }
    }

    // Submit user answers and get results
    async submitAnswers(userData) {
        try {
            const data = await this.makeRequest('submitAnswers', {
                method: 'POST',
                data: userData
            });

            return data;
        } catch (error) {
            console.error('Error submitting answers:', error);
            // Return mock response for development
            return this.getMockSubmissionResponse(userData);
        }
    }

    // Get user statistics and T-score
    async getUserStats(username) {
        try {
            const data = await this.makeRequest('getUserStats', {
                method: 'GET',
                data: { username }
            });

            return data;
        } catch (error) {
            console.error('Error fetching user stats:', error);
            // Return mock stats for development
            return this.getMockUserStats(username);
        }
    }

    // Get platform statistics for admin
    async getPlatformStats() {
        try {
            const data = await this.makeRequest('getPlatformStats', {
                method: 'GET'
            });

            return data;
        } catch (error) {
            console.error('Error fetching platform stats:', error);
            return this.getMockPlatformStats();
        }
    }

    // Admin: Get all users
    async getAllUsers() {
        try {
            const data = await this.makeRequest('getAllUsers', {
                method: 'GET'
            });

            return data;
        } catch (error) {
            console.error('Error fetching users:', error);
            return this.getMockUsers();
        }
    }

    // Admin: Get all questions
    async getAllQuestions() {
        try {
            const data = await this.makeRequest('getAllQuestions', {
                method: 'GET'
            });

            return data;
        } catch (error) {
            console.error('Error fetching all questions:', error);
            return this.getMockAllQuestions();
        }
    }

    // Admin: Add/Update question
    async saveQuestion(questionData) {
        try {
            const data = await this.makeRequest('saveQuestion', {
                method: 'POST',
                data: questionData
            });

            return data;
        } catch (error) {
            console.error('Error saving question:', error);
            throw error;
        }
    }

    // Admin: Delete question
    async deleteQuestion(questionId) {
        try {
            const data = await this.makeRequest('deleteQuestion', {
                method: 'POST',
                data: { id: questionId }
            });

            return data;
        } catch (error) {
            console.error('Error deleting question:', error);
            throw error;
        }
    }

    // Mock data for development/testing
    getMockQuestions(level, count) {
        const difficulties = ['Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
        const mockQuestions = [];

        for (let i = 0; i < count; i++) {
            mockQuestions.push({
                id: Math.random().toString(36).substr(2, 9),
                question: `Sample ${difficulties[level - 1]} question ${i + 1}. What is the correct answer?`,
                choices: {
                    A: 'Option A - First choice',
                    B: 'Option B - Second choice',
                    C: 'Option C - Third choice',
                    D: 'Option D - Fourth choice'
                },
                correctAnswer: 'A',
                hint: `This is a hint for the ${difficulties[level - 1]} question.`,
                difficulty: level,
                category: 'General Knowledge'
            });
        }

        return { questions: mockQuestions };
    }

    getMockSubmissionResponse(userData) {
        const totalQuestions = userData.answers.length;
        const correctAnswers = userData.answers.filter(a => a.isCorrect).length;
        const rawScore = (correctAnswers / totalQuestions) * 100;
        
        // Mock T-score calculation
        const populationMean = 65;
        const populationStdDev = 15;
        const tScore = 50 + 10 * ((rawScore - populationMean) / populationStdDev);

        return {
            success: true,
            results: {
                totalQuestions,
                correctAnswers,
                rawScore,
                tScore: Math.round(tScore * 10) / 10,
                populationMean,
                populationStdDev,
                percentile: Math.round((1 - (tScore - 50) / 50) * 100)
            }
        };
    }

    getMockUserStats(username) {
        return {
            user: username,
            totalAttempts: Math.floor(Math.random() * 10) + 1,
            bestTScore: Math.round((Math.random() * 40 + 40) * 10) / 10,
            averageAccuracy: Math.round((Math.random() * 30 + 60) * 10) / 10,
            lastQuizDate: new Date().toISOString().split('T')[0],
            difficultyProgress: {
                1: Math.round(Math.random() * 100),
                2: Math.round(Math.random() * 100),
                3: Math.round(Math.random() * 100),
                4: Math.round(Math.random() * 100),
                5: Math.round(Math.random() * 100)
            }
        };
    }

    getMockPlatformStats() {
        return {
            totalUsers: 156,
            totalQuestions: 250,
            totalAttempts: 1247,
            averageTScore: 52.3,
            tScoreDistribution: [
                { range: '0-30', count: 12 },
                { range: '30-40', count: 23 },
                { range: '40-50', count: 45 },
                { range: '50-60', count: 38 },
                { range: '60-70', count: 28 },
                { range: '70+', count: 10 }
            ],
            dailyActivity: [
                { date: '2024-01-01', attempts: 23 },
                { date: '2024-01-02', attempts: 31 },
                { date: '2024-01-03', attempts: 28 },
                { date: '2024-01-04', attempts: 35 },
                { date: '2024-01-05', attempts: 42 },
                { date: '2024-01-06', attempts: 38 },
                { date: '2024-01-07', attempts: 29 }
            ]
        };
    }

    getMockUsers() {
        const users = [];
        const names = ['Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson', 'Eva Brown'];
        
        for (let i = 0; i < names.length; i++) {
            users.push({
                name: names[i],
                lastQuiz: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                bestTScore: Math.round((Math.random() * 40 + 40) * 10) / 10,
                attempts: Math.floor(Math.random() * 15) + 1,
                avgAccuracy: Math.round((Math.random() * 30 + 60) * 10) / 10
            });
        }
        
        return { users };
    }

    getMockAllQuestions() {
        const questions = [];
        const difficulties = ['Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
        const categories = ['Math', 'Science', 'History', 'Literature', 'General Knowledge'];
        
        for (let i = 1; i <= 20; i++) {
            const difficulty = Math.floor(Math.random() * 5) + 1;
            questions.push({
                id: i,
                question: `Sample question ${i}. What is the correct answer?`,
                difficulty,
                difficultyName: difficulties[difficulty - 1],
                category: categories[Math.floor(Math.random() * categories.length)],
                usageCount: Math.floor(Math.random() * 100),
                successRate: Math.round((Math.random() * 40 + 40) * 10) / 10
            });
        }
        
        return { questions };
    }
}

// Create global API instance
const api = new AdaptiveAPI();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdaptiveAPI;
}

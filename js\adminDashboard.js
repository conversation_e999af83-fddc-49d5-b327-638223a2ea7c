// Enhanced Admin Dashboard Logic
class AdminDashboard {
    constructor() {
        this.currentTab = 'overview';
        this.isLoggedIn = false;
        this.csvData = null;
        this.settings = this.loadSettings();
        this.testFileManager = new TestFileManager();

        this.initializeEventListeners();
        this.checkLoginStatus();
    }

    initializeEventListeners() {
        // Login form
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // CSV Upload functionality
        this.initializeCSVUpload();

        // Settings functionality
        this.initializeSettings();

        // General admin actions
        document.getElementById('refresh-data')?.addEventListener('click', () => {
            this.refreshAllData();
        });

        document.getElementById('logout')?.addEventListener('click', () => {
            this.logout();
        });

        // Question management
        document.getElementById('manage-titles')?.addEventListener('click', () => {
            this.openTitleModal();
        });

        // Test file management
        document.getElementById('create-new-test')?.addEventListener('click', () => {
            this.openTestFileModal();
        });

        document.getElementById('import-test-file')?.addEventListener('click', () => {
            this.importTestFile();
        });
    }

    initializeCSVUpload() {
        const uploadArea = document.getElementById('csv-upload-area');
        const fileInput = document.getElementById('csv-file-input');
        const browseBtn = document.getElementById('browse-csv');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        });

        // Browse button
        browseBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0]);
            }
        });

        // Upload actions
        document.getElementById('confirm-upload')?.addEventListener('click', () => {
            this.confirmCSVUpload();
        });

        document.getElementById('cancel-upload')?.addEventListener('click', () => {
            this.cancelCSVUpload();
        });

        // Template and backup
        document.getElementById('download-template')?.addEventListener('click', () => {
            this.downloadCSVTemplate();
        });

        document.getElementById('backup-data')?.addEventListener('click', () => {
            this.backupData();
        });

        // Data management actions
        document.getElementById('export-questions')?.addEventListener('click', () => {
            this.exportQuestions();
        });

        document.getElementById('export-results')?.addEventListener('click', () => {
            this.exportResults();
        });

        document.getElementById('clear-all-data')?.addEventListener('click', () => {
            this.clearAllData();
        });
    }

    initializeSettings() {
        // T-Score settings
        document.getElementById('save-tscore-settings')?.addEventListener('click', () => {
            this.saveTScoreSettings();
        });

        // Quiz settings
        document.getElementById('save-quiz-settings')?.addEventListener('click', () => {
            this.saveQuizSettings();
        });

        // User settings
        document.getElementById('save-user-settings')?.addEventListener('click', () => {
            this.saveUserSettings();
        });

        // Appearance settings
        document.getElementById('save-appearance-settings')?.addEventListener('click', () => {
            this.saveAppearanceSettings();
        });

        // Notification settings
        document.getElementById('save-notification-settings')?.addEventListener('click', () => {
            this.saveNotificationSettings();
        });

        // Maintenance actions
        document.getElementById('optimize-database')?.addEventListener('click', () => {
            this.optimizeDatabase();
        });

        document.getElementById('recalculate-tscores')?.addEventListener('click', () => {
            this.recalculateTScores();
        });

        document.getElementById('cleanup-old-data')?.addEventListener('click', () => {
            this.cleanupOldData();
        });
    }

    checkLoginStatus() {
        const isLoggedIn = localStorage.getItem('adminLoggedIn') === 'true';
        if (isLoggedIn) {
            this.isLoggedIn = true;
            this.showDashboard();
        } else {
            this.showLogin();
        }
    }

    handleLogin() {
        const username = document.getElementById('admin-username').value;
        const password = document.getElementById('admin-password').value;

        // Simple authentication (in production, use proper authentication)
        if (username === 'admin' && password === 'admin123') {
            this.isLoggedIn = true;
            localStorage.setItem('adminLoggedIn', 'true');
            this.showDashboard();
        } else {
            alert('Invalid credentials. Use admin/admin123 for demo.');
        }
    }

    logout() {
        this.isLoggedIn = false;
        localStorage.removeItem('adminLoggedIn');
        this.showLogin();
    }

    showLogin() {
        document.getElementById('admin-login').classList.add('active');
        document.getElementById('admin-dashboard').classList.remove('active');
    }

    showDashboard() {
        document.getElementById('admin-login').classList.remove('active');
        document.getElementById('admin-dashboard').classList.add('active');
        this.loadDashboardData();
    }

    async loadDashboardData() {
        try {
            // Wait for data manager to initialize
            if (!dataManager.db) {
                setTimeout(() => this.loadDashboardData(), 100);
                return;
            }

            await this.loadOverviewData();
            await this.loadUsersData();
            await this.loadQuestionsData();
            this.loadSettingsData();
            this.updateSystemInfo();
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('Failed to load dashboard data.');
        }
    }

    async loadOverviewData() {
        const analytics = await dataManager.getAnalytics();
        
        // Update overview stats
        document.getElementById('total-users').textContent = analytics.totalUsers;
        document.getElementById('total-quizzes').textContent = analytics.totalQuizzes;
        document.getElementById('avg-tscore').textContent = analytics.averageTScore.toFixed(1);
        document.getElementById('total-questions').textContent = analytics.totalCategories;

        // Update data management counts
        document.getElementById('total-questions-count').textContent = analytics.totalQuizzes;
        document.getElementById('total-categories-count').textContent = analytics.totalCategories;
        document.getElementById('total-users-count').textContent = analytics.totalUsers;
        document.getElementById('total-results-count').textContent = analytics.totalQuizzes;
    }

    async loadUsersData() {
        const users = await dataManager.getAllUsers();
        const tbody = document.querySelector('#users-table tbody');
        
        tbody.innerHTML = users.map(user => `
            <tr>
                <td>${user.username}</td>
                <td>${user.lastQuizDate ? new Date(user.lastQuizDate).toLocaleDateString() : 'Never'}</td>
                <td>${user.bestTScore?.toFixed(1) || '--'}</td>
                <td>${user.totalAttempts || 0}</td>
                <td>${user.lastRawScore?.toFixed(1) || '--'}%</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="adminDashboard.viewUserDetails('${user.username}')">View</button>
                    <button class="btn btn-sm btn-outline" onclick="adminDashboard.deleteUser('${user.username}')">Delete</button>
                </td>
            </tr>
        `).join('');
    }

    async loadQuestionsData() {
        const questions = await dataManager.getAllQuestions();
        const tbody = document.querySelector('#questions-table tbody');
        
        tbody.innerHTML = questions.map(question => `
            <tr>
                <td>${question.id}</td>
                <td>${question.question.substring(0, 50)}...</td>
                <td>${this.getDifficultyName(question.difficulty)}</td>
                <td>${question.category}</td>
                <td>--</td>
                <td>--</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="adminDashboard.editQuestion('${question.id}')">Edit</button>
                    <button class="btn btn-sm btn-outline" onclick="adminDashboard.deleteQuestion('${question.id}')">Delete</button>
                </td>
            </tr>
        `).join('');
    }

    loadSettingsData() {
        // Load current settings into form fields
        document.getElementById('default-mean').value = this.settings.tScore.defaultMean;
        document.getElementById('default-stddev').value = this.settings.tScore.defaultStdDev;
        document.getElementById('min-sample-size').value = this.settings.tScore.minSampleSize;
        
        document.getElementById('default-quiz-length').value = this.settings.quiz.defaultLength;
        document.getElementById('passing-score').value = this.settings.quiz.passingScore;
        document.getElementById('excellence-threshold').value = this.settings.quiz.excellenceThreshold;
        document.getElementById('enable-hints').checked = this.settings.quiz.enableHints;
        document.getElementById('enable-adaptive').checked = this.settings.quiz.enableAdaptive;
    }

    updateSystemInfo() {
        // Update system information
        document.getElementById('system-version').textContent = '1.0.0';
        document.getElementById('last-backup').textContent = 
            localStorage.getItem('lastBackup') || 'Never';
        
        // Calculate approximate database size
        this.calculateDatabaseSize().then(size => {
            document.getElementById('db-size').textContent = this.formatBytes(size);
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
    }

    async handleFileSelect(file) {
        if (!file.name.endsWith('.csv')) {
            alert('Please select a CSV file.');
            return;
        }

        const status = document.getElementById('upload-status');
        status.innerHTML = `<p>📁 Selected: ${file.name} (${this.formatBytes(file.size)})</p>`;

        try {
            const text = await file.text();
            this.csvData = this.parseCSV(text);
            this.showCSVPreview();
        } catch (error) {
            console.error('Error reading CSV file:', error);
            status.innerHTML = `<p style="color: red;">❌ Error reading file: ${error.message}</p>`;
        }
    }

    parseCSV(text) {
        const lines = text.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            if (values.length === headers.length) {
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index];
                });
                data.push(row);
            }
        }

        return { headers, data };
    }

    parseCSVLine(line) {
        const values = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        values.push(current.trim());
        return values.map(v => v.replace(/^"|"$/g, ''));
    }

    showCSVPreview() {
        const preview = document.getElementById('csv-preview');
        const table = document.getElementById('csv-preview-table');
        const header = document.getElementById('csv-preview-header');
        const body = document.getElementById('csv-preview-body');

        // Validate CSV data
        const validation = this.validateCSVData();

        // Update preview stats
        document.getElementById('preview-total').textContent = this.csvData.data.length;
        document.getElementById('preview-valid').textContent = validation.valid;
        document.getElementById('preview-errors').textContent = validation.errors.length;

        // Show validation results if there are errors
        if (validation.errors.length > 0) {
            this.showValidationResults(validation.errors);
        }

        // Create table header
        header.innerHTML = `<tr>${this.csvData.headers.map(h => `<th>${h}</th>`).join('')}</tr>`;

        // Create table body (show first 10 rows)
        const previewData = this.csvData.data.slice(0, 10);
        body.innerHTML = previewData.map((row, index) => {
            const isValid = validation.validRows.includes(index);
            return `<tr class="${isValid ? '' : 'invalid-row'}">
                ${this.csvData.headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
            </tr>`;
        }).join('');

        preview.style.display = 'block';
    }

    validateCSVData() {
        const requiredFields = ['id', 'question', 'choiceA', 'choiceB', 'choiceC', 'choiceD', 'correctAnswer', 'difficulty', 'category'];
        const errors = [];
        const validRows = [];

        // Check if all required headers are present
        const missingHeaders = requiredFields.filter(field => !this.csvData.headers.includes(field));
        if (missingHeaders.length > 0) {
            errors.push(`Missing required headers: ${missingHeaders.join(', ')}`);
        }

        // Validate each row
        this.csvData.data.forEach((row, index) => {
            const rowErrors = [];

            // Check required fields
            requiredFields.forEach(field => {
                if (!row[field] || row[field].trim() === '') {
                    rowErrors.push(`Missing ${field}`);
                }
            });

            // Validate difficulty (should be 1-5)
            const difficulty = parseInt(row.difficulty);
            if (isNaN(difficulty) || difficulty < 1 || difficulty > 5) {
                rowErrors.push('Difficulty must be between 1 and 5');
            }

            // Validate correct answer (should be A, B, C, or D)
            if (row.correctAnswer && !['A', 'B', 'C', 'D'].includes(row.correctAnswer.toUpperCase())) {
                rowErrors.push('Correct answer must be A, B, C, or D');
            }

            if (rowErrors.length === 0) {
                validRows.push(index);
            } else {
                errors.push(`Row ${index + 2}: ${rowErrors.join(', ')}`);
            }
        });

        return {
            valid: validRows.length,
            errors,
            validRows
        };
    }

    showValidationResults(errors) {
        const validation = document.getElementById('csv-validation');
        const results = document.getElementById('validation-results');

        results.innerHTML = `
            <div class="validation-errors">
                <h4>❌ Validation Errors (${errors.length})</h4>
                <ul>
                    ${errors.slice(0, 20).map(error => `<li>${error}</li>`).join('')}
                    ${errors.length > 20 ? `<li>... and ${errors.length - 20} more errors</li>` : ''}
                </ul>
            </div>
        `;

        validation.style.display = 'block';
    }

    async confirmCSVUpload() {
        const validation = this.validateCSVData();

        if (validation.errors.length > 0 && document.getElementById('validate-before-upload').checked) {
            if (!confirm(`There are ${validation.errors.length} validation errors. Continue anyway?`)) {
                return;
            }
        }

        try {
            const progress = document.getElementById('upload-progress');
            const progressBar = document.getElementById('upload-progress-bar');
            const progressText = document.getElementById('upload-progress-text');

            progress.style.display = 'block';

            // Convert CSV data to question format
            const questions = this.csvData.data
                .filter((_, index) => validation.validRows.includes(index))
                .map(row => ({
                    id: row.id,
                    question: row.question,
                    choices: {
                        A: row.choiceA,
                        B: row.choiceB,
                        C: row.choiceC,
                        D: row.choiceD
                    },
                    correctAnswer: row.correctAnswer.toUpperCase(),
                    hint: row.hint || '',
                    difficulty: parseInt(row.difficulty),
                    category: row.category
                }));

            // Clear existing questions if requested
            if (document.getElementById('replace-existing').checked) {
                // This would clear existing questions in a real implementation
                console.log('Replacing existing questions...');
            }

            // Upload questions in batches
            const batchSize = 10;
            for (let i = 0; i < questions.length; i += batchSize) {
                const batch = questions.slice(i, i + batchSize);
                await dataManager.saveQuestions(batch);

                const progress = ((i + batch.length) / questions.length) * 100;
                progressBar.style.width = `${progress}%`;
                progressText.textContent = `${Math.round(progress)}%`;

                // Small delay to show progress
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Success
            progressText.textContent = '✅ Upload Complete!';
            setTimeout(() => {
                this.cancelCSVUpload();
                this.refreshAllData();
                alert(`Successfully uploaded ${questions.length} questions!`);
            }, 1000);

        } catch (error) {
            console.error('Error uploading CSV:', error);
            alert('Error uploading CSV: ' + error.message);
        }
    }

    cancelCSVUpload() {
        document.getElementById('csv-preview').style.display = 'none';
        document.getElementById('csv-validation').style.display = 'none';
        document.getElementById('upload-progress').style.display = 'none';
        document.getElementById('upload-status').innerHTML = '<p>No file selected</p>';
        document.getElementById('csv-file-input').value = '';
        this.csvData = null;
    }

    downloadCSVTemplate() {
        const template = `id,question,choiceA,choiceB,choiceC,choiceD,correctAnswer,hint,difficulty,category
1,"What is the capital of France?","London","Berlin","Paris","Madrid","C","France is known for the Eiffel Tower",1,"Geography"
2,"Which planet is known as the Red Planet?","Venus","Mars","Jupiter","Saturn","B","This planet appears reddish due to iron oxide",1,"Science"
3,"What is 2 + 2?","3","4","5","6","B","Basic addition",1,"Math"`;

        this.downloadFile('quiz_template.csv', template, 'text/csv');
    }

    async backupData() {
        try {
            const [users, results, questions, categories] = await Promise.all([
                dataManager.getAllUsers(),
                dataManager.getAllResults(),
                dataManager.getAllQuestions(),
                dataManager.getAllCategories()
            ]);

            const backup = {
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                data: {
                    users,
                    results,
                    questions,
                    categories
                }
            };

            const backupJson = JSON.stringify(backup, null, 2);
            const filename = `quiz_backup_${new Date().toISOString().split('T')[0]}.json`;

            this.downloadFile(filename, backupJson, 'application/json');
            localStorage.setItem('lastBackup', new Date().toISOString());
            this.updateSystemInfo();

            alert('Backup created successfully!');
        } catch (error) {
            console.error('Error creating backup:', error);
            alert('Error creating backup: ' + error.message);
        }
    }

    async exportQuestions() {
        try {
            const questions = await dataManager.getAllQuestions();
            const csv = this.convertToCSV(questions, [
                'id', 'question', 'choiceA', 'choiceB', 'choiceC', 'choiceD',
                'correctAnswer', 'hint', 'difficulty', 'category'
            ]);

            const filename = `questions_export_${new Date().toISOString().split('T')[0]}.csv`;
            this.downloadFile(filename, csv, 'text/csv');
        } catch (error) {
            console.error('Error exporting questions:', error);
            alert('Error exporting questions: ' + error.message);
        }
    }

    async exportResults() {
        try {
            const results = await dataManager.getAllResults();
            const csv = this.convertToCSV(results, [
                'username', 'category', 'totalQuestions', 'correctAnswers',
                'rawScore', 'tScore', 'completedAt'
            ]);

            const filename = `results_export_${new Date().toISOString().split('T')[0]}.csv`;
            this.downloadFile(filename, csv, 'text/csv');
        } catch (error) {
            console.error('Error exporting results:', error);
            alert('Error exporting results: ' + error.message);
        }
    }

    convertToCSV(data, headers) {
        const csvHeaders = headers.join(',');
        const csvRows = data.map(item => {
            return headers.map(header => {
                let value = item[header];
                if (typeof value === 'object' && value !== null) {
                    value = JSON.stringify(value);
                }
                if (typeof value === 'string' && value.includes(',')) {
                    value = `"${value}"`;
                }
                return value || '';
            }).join(',');
        });

        return [csvHeaders, ...csvRows].join('\n');
    }

    downloadFile(filename, content, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Settings Management
    saveTScoreSettings() {
        this.settings.tScore = {
            defaultMean: parseFloat(document.getElementById('default-mean').value),
            defaultStdDev: parseFloat(document.getElementById('default-stddev').value),
            minSampleSize: parseInt(document.getElementById('min-sample-size').value)
        };
        this.saveSettings();
        alert('T-Score settings saved successfully!');
    }

    saveQuizSettings() {
        this.settings.quiz = {
            defaultLength: parseInt(document.getElementById('default-quiz-length').value),
            passingScore: parseInt(document.getElementById('passing-score').value),
            excellenceThreshold: parseInt(document.getElementById('excellence-threshold').value),
            enableHints: document.getElementById('enable-hints').checked,
            enableAdaptive: document.getElementById('enable-adaptive').checked
        };
        this.saveSettings();
        alert('Quiz settings saved successfully!');
    }

    saveUserSettings() {
        this.settings.user = {
            allowGuestUsers: document.getElementById('allow-guest-users').checked,
            requireEmail: document.getElementById('require-email').checked,
            maxAttemptsPerDay: parseInt(document.getElementById('max-attempts-per-day').value)
        };
        this.saveSettings();
        alert('User settings saved successfully!');
    }

    saveAppearanceSettings() {
        this.settings.appearance = {
            theme: document.getElementById('theme-select').value,
            primaryColor: document.getElementById('primary-color').value,
            showAnimations: document.getElementById('show-animations').checked
        };
        this.saveSettings();
        this.applyTheme();
        alert('Appearance settings saved successfully!');
    }

    saveNotificationSettings() {
        this.settings.notifications = {
            emailNotifications: document.getElementById('email-notifications').checked,
            adminEmail: document.getElementById('admin-email').value,
            dailyReports: document.getElementById('daily-reports').checked
        };
        this.saveSettings();
        alert('Notification settings saved successfully!');
    }

    loadSettings() {
        const defaultSettings = {
            tScore: {
                defaultMean: 47,
                defaultStdDev: 12,
                minSampleSize: 5
            },
            quiz: {
                defaultLength: 20,
                passingScore: 70,
                excellenceThreshold: 70,
                enableHints: true,
                enableAdaptive: true
            },
            user: {
                allowGuestUsers: true,
                requireEmail: false,
                maxAttemptsPerDay: 10
            },
            appearance: {
                theme: 'default',
                primaryColor: '#667eea',
                showAnimations: true
            },
            notifications: {
                emailNotifications: false,
                adminEmail: '',
                dailyReports: false
            }
        };

        const saved = localStorage.getItem('adminSettings');
        return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }

    saveSettings() {
        localStorage.setItem('adminSettings', JSON.stringify(this.settings));
    }

    applyTheme() {
        const root = document.documentElement;
        if (this.settings.appearance.theme === 'dark') {
            root.classList.add('dark-theme');
        } else {
            root.classList.remove('dark-theme');
        }

        root.style.setProperty('--primary-color', this.settings.appearance.primaryColor);
    }

    // Maintenance Functions
    async optimizeDatabase() {
        if (!confirm('This will optimize the database. Continue?')) return;

        try {
            // In a real implementation, this would optimize IndexedDB
            await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate optimization
            alert('Database optimized successfully!');
        } catch (error) {
            console.error('Error optimizing database:', error);
            alert('Error optimizing database: ' + error.message);
        }
    }

    async recalculateTScores() {
        if (!confirm('This will recalculate all T-Scores. This may take a while. Continue?')) return;

        try {
            const results = await dataManager.getAllResults();
            let processed = 0;

            for (let result of results) {
                const newTScoreData = await dataManager.calculateTScore(result.rawScore, result.category);
                result.tScore = newTScoreData.tScore;
                // In a real implementation, update the result in the database
                processed++;
            }

            alert(`Recalculated T-Scores for ${processed} results!`);
            this.refreshAllData();
        } catch (error) {
            console.error('Error recalculating T-Scores:', error);
            alert('Error recalculating T-Scores: ' + error.message);
        }
    }

    async cleanupOldData() {
        if (!confirm('This will remove data older than 1 year. Continue?')) return;

        try {
            const cutoffDate = new Date();
            cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);

            // In a real implementation, this would remove old data
            alert('Old data cleanup completed!');
        } catch (error) {
            console.error('Error cleaning up old data:', error);
            alert('Error cleaning up old data: ' + error.message);
        }
    }

    async clearAllData() {
        const confirmation = prompt('Type "DELETE ALL DATA" to confirm this action:');
        if (confirmation !== 'DELETE ALL DATA') {
            alert('Action cancelled.');
            return;
        }

        try {
            await dataManager.clearAllData();
            alert('All data has been cleared!');
            this.refreshAllData();
        } catch (error) {
            console.error('Error clearing data:', error);
            alert('Error clearing data: ' + error.message);
        }
    }

    // User Management Functions
    async viewUserDetails(username) {
        try {
            const user = await dataManager.getUser(username);
            const results = await dataManager.getUserResults(username);

            const details = `
User: ${username}
Total Attempts: ${user.totalAttempts || 0}
Best T-Score: ${user.bestTScore?.toFixed(1) || '--'}
Last Quiz: ${user.lastQuizDate || 'Never'}
First Quiz: ${user.firstQuizDate || 'Never'}
Recent Results: ${results.length}
            `;

            alert(details);
        } catch (error) {
            console.error('Error viewing user details:', error);
            alert('Error loading user details: ' + error.message);
        }
    }

    async deleteUser(username) {
        if (!confirm(`Are you sure you want to delete user "${username}" and all their data?`)) {
            return;
        }

        try {
            // In a real implementation, this would delete the user and their results
            alert(`User "${username}" has been deleted.`);
            this.loadUsersData();
        } catch (error) {
            console.error('Error deleting user:', error);
            alert('Error deleting user: ' + error.message);
        }
    }

    // Question Management Functions
    editQuestion(questionId) {
        // In a real implementation, this would open a modal to edit the question
        alert(`Edit question ${questionId} - Feature coming soon!`);
    }

    async deleteQuestion(questionId) {
        if (!confirm(`Are you sure you want to delete question ${questionId}?`)) {
            return;
        }

        try {
            // In a real implementation, this would delete the question
            alert(`Question ${questionId} has been deleted.`);
            this.loadQuestionsData();
        } catch (error) {
            console.error('Error deleting question:', error);
            alert('Error deleting question: ' + error.message);
        }
    }

    // Utility Functions
    async refreshAllData() {
        await this.loadDashboardData();
        alert('Data refreshed successfully!');
    }

    async calculateDatabaseSize() {
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                return estimate.usage || 0;
            }
            return 0;
        } catch (error) {
            console.error('Error calculating database size:', error);
            return 0;
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getDifficultyName(difficulty) {
        const names = {
            1: 'Very Easy',
            2: 'Easy',
            3: 'Medium',
            4: 'Hard',
            5: 'Very Hard'
        };
        return names[difficulty] || 'Unknown';
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1000;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 5000);
    }

    // Title Management Functions
    openTitleModal() {
        const modal = document.getElementById('title-modal');
        modal.style.display = 'flex';

        // Load current title settings
        this.loadTitleSettings();

        // Setup form submission
        const form = document.getElementById('title-form');
        form.onsubmit = (e) => {
            e.preventDefault();
            this.saveTitleSettings();
        };
    }

    loadTitleSettings() {
        // Load from localStorage or use defaults
        const settings = JSON.parse(localStorage.getItem('siteSettings') || '{}');

        document.getElementById('site-title').value = settings.title || 'Adaptive Learning Platform';
        document.getElementById('site-subtitle').value = settings.subtitle || 'Your comprehensive quiz and assessment system';
        document.getElementById('welcome-message').value = settings.welcomeMessage || 'Welcome to our adaptive learning platform where you can test your knowledge and track your progress!';
        document.getElementById('footer-text').value = settings.footer || '© 2024 Adaptive Learning Platform';
    }

    saveTitleSettings() {
        const settings = {
            title: document.getElementById('site-title').value,
            subtitle: document.getElementById('site-subtitle').value,
            welcomeMessage: document.getElementById('welcome-message').value,
            footer: document.getElementById('footer-text').value,
            lastUpdated: new Date().toISOString()
        };

        localStorage.setItem('siteSettings', JSON.stringify(settings));

        // Apply changes immediately to current page
        this.applyTitleSettings(settings);

        this.closeTitleModal();
        this.showNotification('Title settings saved successfully!', 'success');
    }

    applyTitleSettings(settings) {
        // Update page title
        document.title = settings.title;

        // Update any title elements on the page
        const titleElements = document.querySelectorAll('h1, .site-title');
        titleElements.forEach(el => {
            if (el.textContent.includes('Adaptive Learning Platform')) {
                el.textContent = settings.title;
            }
        });
    }

    closeTitleModal() {
        document.getElementById('title-modal').style.display = 'none';
    }

    // Question Editing Functions
    openEditQuestionModal(questionId) {
        const modal = document.getElementById('edit-question-modal');
        modal.style.display = 'flex';

        // Load question data
        this.loadQuestionForEdit(questionId);

        // Setup form submission
        const form = document.getElementById('edit-question-form');
        form.onsubmit = (e) => {
            e.preventDefault();
            this.saveQuestionEdit(questionId);
        };
    }

    async loadQuestionForEdit(questionId) {
        try {
            // Get question from CSV engine or storage
            const questions = await this.csvEngine.loadQuestions();
            const question = questions.find(q => q.id == questionId);

            if (!question) {
                this.showNotification('Question not found!', 'error');
                return;
            }

            // Populate form fields
            document.getElementById('edit-question-id').value = question.id;
            document.getElementById('edit-question-text').value = question.question;
            document.getElementById('edit-choice-a').value = question.choices.A;
            document.getElementById('edit-choice-b').value = question.choices.B;
            document.getElementById('edit-choice-c').value = question.choices.C;
            document.getElementById('edit-choice-d').value = question.choices.D;
            document.getElementById('edit-correct-answer').value = question.correctAnswer;
            document.getElementById('edit-difficulty').value = question.difficulty;
            document.getElementById('edit-category').value = question.category;
            document.getElementById('edit-hint').value = question.hint || '';

        } catch (error) {
            console.error('Error loading question for edit:', error);
            this.showNotification('Error loading question data!', 'error');
        }
    }

    async saveQuestionEdit(questionId) {
        try {
            const updatedQuestion = {
                id: questionId,
                question: document.getElementById('edit-question-text').value,
                choices: {
                    A: document.getElementById('edit-choice-a').value,
                    B: document.getElementById('edit-choice-b').value,
                    C: document.getElementById('edit-choice-c').value,
                    D: document.getElementById('edit-choice-d').value
                },
                correctAnswer: document.getElementById('edit-correct-answer').value,
                difficulty: parseInt(document.getElementById('edit-difficulty').value),
                category: document.getElementById('edit-category').value,
                hint: document.getElementById('edit-hint').value
            };

            // Validate question data
            if (!this.validateQuestionData(updatedQuestion)) {
                return;
            }

            // Update question in storage
            await this.updateQuestionInStorage(updatedQuestion);

            this.closeEditQuestionModal();
            this.showNotification('Question updated successfully!', 'success');
            this.refreshQuestionsTable();

        } catch (error) {
            console.error('Error saving question edit:', error);
            this.showNotification('Error saving question changes!', 'error');
        }
    }

    validateQuestionData(question) {
        if (!question.question.trim()) {
            this.showNotification('Question text is required!', 'error');
            return false;
        }

        if (!question.choices.A.trim() || !question.choices.B.trim() ||
            !question.choices.C.trim() || !question.choices.D.trim()) {
            this.showNotification('All choice options are required!', 'error');
            return false;
        }

        if (!question.correctAnswer) {
            this.showNotification('Correct answer must be selected!', 'error');
            return false;
        }

        if (!question.category.trim()) {
            this.showNotification('Category is required!', 'error');
            return false;
        }

        return true;
    }

    async updateQuestionInStorage(updatedQuestion) {
        // This would typically update the CSV file or database
        // For now, we'll update localStorage and notify about CSV update needed
        const questions = JSON.parse(localStorage.getItem('editedQuestions') || '[]');
        const existingIndex = questions.findIndex(q => q.id == updatedQuestion.id);

        if (existingIndex >= 0) {
            questions[existingIndex] = updatedQuestion;
        } else {
            questions.push(updatedQuestion);
        }

        localStorage.setItem('editedQuestions', JSON.stringify(questions));

        // Show notification about CSV update
        this.showNotification('Question updated in local storage. Remember to export and update your CSV file!', 'warning');
    }

    async deleteQuestion(questionId) {
        if (!confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
            return;
        }

        try {
            // Mark question as deleted
            const deletedQuestions = JSON.parse(localStorage.getItem('deletedQuestions') || '[]');
            deletedQuestions.push(questionId);
            localStorage.setItem('deletedQuestions', JSON.stringify(deletedQuestions));

            this.closeEditQuestionModal();
            this.showNotification('Question marked for deletion. Update your CSV file to apply changes.', 'warning');
            this.refreshQuestionsTable();

        } catch (error) {
            console.error('Error deleting question:', error);
            this.showNotification('Error deleting question!', 'error');
        }
    }

    closeEditQuestionModal() {
        document.getElementById('edit-question-modal').style.display = 'none';
    }

    refreshQuestionsTable() {
        // Refresh the questions table display
        // This would typically reload the questions data and update the table
        this.showNotification('Questions table refreshed', 'info');
    }

    // Test File Management Functions
    openTestFileModal(testId = null) {
        const modal = document.getElementById('test-file-modal');
        const title = document.getElementById('test-file-modal-title');
        const form = document.getElementById('test-file-form');

        modal.style.display = 'flex';

        if (testId) {
            // Edit existing test file
            title.textContent = '✏️ Edit Test File';
            this.loadTestFileForEdit(testId);
        } else {
            // Create new test file
            title.textContent = '📁 Create New Test File';
            form.reset();
        }

        form.onsubmit = (e) => {
            e.preventDefault();
            if (testId) {
                this.updateTestFile(testId);
            } else {
                this.createNewTestFile();
            }
        };
    }

    loadTestFileForEdit(testId) {
        const testFile = this.testFileManager.getTestFile(testId);
        if (!testFile) return;

        document.getElementById('test-file-name').value = testFile.name;
        document.getElementById('test-file-id').value = testFile.id;
        document.getElementById('test-description').value = testFile.description || '';
        document.getElementById('test-category').value = testFile.category;
        document.getElementById('test-difficulty').value = testFile.difficulty;
        document.getElementById('test-type').value = testFile.type;
        document.getElementById('test-tags').value = testFile.tags.join(', ');
        document.getElementById('test-active').checked = testFile.active;
    }

    createNewTestFile() {
        try {
            const formData = {
                name: document.getElementById('test-file-name').value,
                id: document.getElementById('test-file-id').value,
                description: document.getElementById('test-description').value,
                category: document.getElementById('test-category').value,
                difficulty: document.getElementById('test-difficulty').value,
                type: document.getElementById('test-type').value,
                tags: document.getElementById('test-tags').value,
                active: document.getElementById('test-active').checked
            };

            // Validate required fields
            if (!formData.name || !formData.id || !formData.category) {
                this.showNotification('Please fill in all required fields!', 'error');
                return;
            }

            // Check if ID already exists
            if (this.testFileManager.getTestFile(formData.id)) {
                this.showNotification('Test file ID already exists!', 'error');
                return;
            }

            const testFile = this.testFileManager.createTestFile(formData);
            this.closeTestFileModal();
            this.refreshTestFilesDisplay();
            this.showNotification(`Test file "${testFile.name}" created successfully!`, 'success');

            // Ask if user wants to add questions immediately
            if (confirm('Would you like to add questions to this test file now?')) {
                this.openCsvEditor(testFile.id);
            }

        } catch (error) {
            console.error('Error creating test file:', error);
            this.showNotification('Error creating test file!', 'error');
        }
    }

    updateTestFile(testId) {
        try {
            const updates = {
                name: document.getElementById('test-file-name').value,
                description: document.getElementById('test-description').value,
                category: document.getElementById('test-category').value,
                difficulty: document.getElementById('test-difficulty').value,
                type: document.getElementById('test-type').value,
                tags: document.getElementById('test-tags').value.split(',').map(t => t.trim()),
                active: document.getElementById('test-active').checked
            };

            this.testFileManager.updateTestFile(testId, updates);
            this.closeTestFileModal();
            this.refreshTestFilesDisplay();
            this.showNotification('Test file updated successfully!', 'success');

        } catch (error) {
            console.error('Error updating test file:', error);
            this.showNotification('Error updating test file!', 'error');
        }
    }

    closeTestFileModal() {
        document.getElementById('test-file-modal').style.display = 'none';
    }

    openCsvEditor(testId) {
        const modal = document.getElementById('csv-editor-modal');
        const title = document.getElementById('csv-editor-title');
        const testFile = this.testFileManager.getTestFile(testId);

        if (!testFile) {
            this.showNotification('Test file not found!', 'error');
            return;
        }

        title.textContent = `✏️ Edit ${testFile.name}`;
        modal.style.display = 'flex';

        // Load questions for this test
        this.loadQuestionsForEditor(testId);
    }

    async loadQuestionsForEditor(testId) {
        try {
            const questions = await this.testFileManager.loadQuestionsForTest(testId);
            this.currentEditingTestId = testId;
            this.renderCsvEditor(questions);

        } catch (error) {
            console.error('Error loading questions:', error);
            this.showNotification('Error loading questions for editing!', 'error');
        }
    }

    renderCsvEditor(questions) {
        const container = document.getElementById('csv-editor-rows');
        container.innerHTML = '';

        // Update counters
        document.getElementById('question-count').textContent = questions.length;
        const categories = [...new Set(questions.map(q => q.category).filter(Boolean))];
        document.getElementById('category-count').textContent = categories.length;

        // Render each question as a row
        questions.forEach((question, index) => {
            const row = this.createCsvEditorRow(question, index);
            container.appendChild(row);
        });

        // Add empty row for new questions
        const emptyRow = this.createCsvEditorRow(null, questions.length);
        container.appendChild(emptyRow);
    }

    createCsvEditorRow(question, index) {
        const row = document.createElement('div');
        row.className = 'csv-row';
        row.innerHTML = `
            <div class="csv-cell">${index + 1}</div>
            <div class="csv-cell">
                <textarea placeholder="Question text...">${question ? question.question : ''}</textarea>
            </div>
            <div class="csv-cell">
                <input type="text" placeholder="Choice A" value="${question ? question.choices.A : ''}">
            </div>
            <div class="csv-cell">
                <input type="text" placeholder="Choice B" value="${question ? question.choices.B : ''}">
            </div>
            <div class="csv-cell">
                <input type="text" placeholder="Choice C" value="${question ? question.choices.C : ''}">
            </div>
            <div class="csv-cell">
                <input type="text" placeholder="Choice D" value="${question ? question.choices.D : ''}">
            </div>
            <div class="csv-cell">
                <select>
                    <option value="A" ${question && question.correctAnswer === 'A' ? 'selected' : ''}>A</option>
                    <option value="B" ${question && question.correctAnswer === 'B' ? 'selected' : ''}>B</option>
                    <option value="C" ${question && question.correctAnswer === 'C' ? 'selected' : ''}>C</option>
                    <option value="D" ${question && question.correctAnswer === 'D' ? 'selected' : ''}>D</option>
                </select>
            </div>
            <div class="csv-cell">
                <input type="text" placeholder="Hint (optional)" value="${question ? question.hint || '' : ''}">
            </div>
            <div class="csv-cell">
                <select>
                    <option value="1" ${question && question.difficulty === 1 ? 'selected' : ''}>1</option>
                    <option value="2" ${question && question.difficulty === 2 ? 'selected' : ''}>2</option>
                    <option value="3" ${question && question.difficulty === 3 ? 'selected' : ''}>3</option>
                    <option value="4" ${question && question.difficulty === 4 ? 'selected' : ''}>4</option>
                    <option value="5" ${question && question.difficulty === 5 ? 'selected' : ''}>5</option>
                </select>
            </div>
            <div class="csv-cell">
                <input type="text" placeholder="Category" value="${question ? question.category : ''}">
            </div>
            <div class="csv-cell">
                <div class="csv-actions">
                    <button class="btn-duplicate" onclick="duplicateRow(${index})" title="Duplicate">📋</button>
                    <button class="btn-delete" onclick="deleteRow(${index})" title="Delete">🗑️</button>
                </div>
            </div>
        `;

        return row;
    }

    saveCsvChanges() {
        try {
            const rows = document.querySelectorAll('.csv-row');
            const questions = [];

            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('.csv-cell');
                const questionText = cells[1].querySelector('textarea').value.trim();

                if (questionText) {
                    const question = {
                        id: index + 1,
                        question: questionText,
                        choices: {
                            A: cells[2].querySelector('input').value.trim(),
                            B: cells[3].querySelector('input').value.trim(),
                            C: cells[4].querySelector('input').value.trim(),
                            D: cells[5].querySelector('input').value.trim()
                        },
                        correctAnswer: cells[6].querySelector('select').value,
                        hint: cells[7].querySelector('input').value.trim(),
                        difficulty: parseInt(cells[8].querySelector('select').value),
                        category: cells[9].querySelector('input').value.trim() || 'General'
                    };

                    // Validate question
                    if (question.choices.A && question.choices.B && question.choices.C && question.choices.D) {
                        questions.push(question);
                    }
                }
            });

            if (questions.length === 0) {
                this.showNotification('Please add at least one complete question!', 'error');
                return;
            }

            // Save questions
            this.testFileManager.saveQuestionsForTest(this.currentEditingTestId, questions);
            this.closeCsvEditorModal();
            this.refreshTestFilesDisplay();
            this.showNotification(`Saved ${questions.length} questions successfully!`, 'success');

        } catch (error) {
            console.error('Error saving CSV changes:', error);
            this.showNotification('Error saving changes!', 'error');
        }
    }

    closeCsvEditorModal() {
        document.getElementById('csv-editor-modal').style.display = 'none';
        this.currentEditingTestId = null;
    }

    refreshTestFilesDisplay() {
        // Update the test files grid
        this.updateTestFileCards();
        this.updateTestFileStatistics();
    }

    updateTestFileCards() {
        // This would update the visual display of test file cards
        // For now, just update the statistics
        const stats = this.testFileManager.getStatistics();

        document.getElementById('total-test-files').textContent = stats.totalFiles;
        document.getElementById('total-questions').textContent = stats.totalQuestions;
        document.getElementById('active-tests').textContent = stats.activeTests;
        document.getElementById('total-categories').textContent = stats.totalCategories;
    }

    updateTestFileStatistics() {
        const stats = this.testFileManager.getStatistics();

        // Update default test card
        const defaultTest = this.testFileManager.getTestFile('default');
        if (defaultTest) {
            document.getElementById('default-question-count').textContent = defaultTest.questionCount;
            document.getElementById('default-categories').textContent = defaultTest.categories.join(', ');
        }

        // Update pretest card
        const pretestFile = this.testFileManager.getTestFile('pretest');
        if (pretestFile) {
            document.getElementById('pretest-question-count').textContent = pretestFile.questionCount;
        }
    }

    importTestFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleTestFileImport(file);
            }
        };
        input.click();
    }

    async handleTestFileImport(file) {
        try {
            const text = await file.text();
            const questions = this.testFileManager.parseCSV(text);

            if (questions.length === 0) {
                this.showNotification('No valid questions found in the CSV file!', 'error');
                return;
            }

            // Create a new test file for the imported questions
            const testName = file.name.replace('.csv', '');
            const testId = testName.toLowerCase().replace(/[^a-z0-9]/g, '-');

            const testData = {
                id: testId,
                name: testName,
                description: `Imported from ${file.name}`,
                category: 'Imported',
                type: 'multiple-choice',
                difficulty: 'mixed',
                tags: 'imported',
                active: true
            };

            const testFile = this.testFileManager.createTestFile(testData);
            this.testFileManager.saveQuestionsForTest(testId, questions);

            this.refreshTestFilesDisplay();
            this.showNotification(`Imported ${questions.length} questions as "${testName}"!`, 'success');

        } catch (error) {
            console.error('Error importing test file:', error);
            this.showNotification('Error importing test file!', 'error');
        }
    }
}

// Global functions for modal management
function closeEditQuestionModal() {
    if (window.adminDashboard) {
        window.adminDashboard.closeEditQuestionModal();
    }
}

function closeTitleModal() {
    if (window.adminDashboard) {
        window.adminDashboard.closeTitleModal();
    }
}

function deleteQuestion() {
    const questionId = document.getElementById('edit-question-id').value;
    if (window.adminDashboard && questionId) {
        window.adminDashboard.deleteQuestion(questionId);
    }
}

// Test File Management Global Functions
function closeTestFileModal() {
    if (window.adminDashboard) {
        window.adminDashboard.closeTestFileModal();
    }
}

function closeCsvEditorModal() {
    if (window.adminDashboard) {
        window.adminDashboard.closeCsvEditorModal();
    }
}

function editTestFile(testId) {
    if (window.adminDashboard) {
        window.adminDashboard.openTestFileModal(testId);
    }
}

function duplicateTestFile(testId) {
    const newName = prompt('Enter name for the duplicated test:');
    if (newName && window.adminDashboard) {
        try {
            window.adminDashboard.testFileManager.duplicateTestFile(testId, newName);
            window.adminDashboard.refreshTestFilesDisplay();
            window.adminDashboard.showNotification(`Test "${newName}" created successfully!`, 'success');
        } catch (error) {
            window.adminDashboard.showNotification('Error duplicating test file!', 'error');
        }
    }
}

function downloadTestFile(testId) {
    if (window.adminDashboard) {
        try {
            window.adminDashboard.testFileManager.exportTestFile(testId);
        } catch (error) {
            window.adminDashboard.showNotification('Error downloading test file!', 'error');
        }
    }
}

function createNewTestFile() {
    if (window.adminDashboard) {
        window.adminDashboard.openTestFileModal();
    }
}

function addNewQuestion() {
    const container = document.getElementById('csv-editor-rows');
    const currentRows = container.querySelectorAll('.csv-row').length;
    const newRow = window.adminDashboard.createCsvEditorRow(null, currentRows);
    container.appendChild(newRow);
}

function duplicateRow(index) {
    const rows = document.querySelectorAll('.csv-row');
    const sourceRow = rows[index];
    const newRow = sourceRow.cloneNode(true);

    // Update the index in the first cell
    const firstCell = newRow.querySelector('.csv-cell');
    firstCell.textContent = rows.length + 1;

    sourceRow.parentNode.insertBefore(newRow, sourceRow.nextSibling);
}

function deleteRow(index) {
    if (confirm('Are you sure you want to delete this question?')) {
        const rows = document.querySelectorAll('.csv-row');
        if (rows[index]) {
            rows[index].remove();

            // Update row numbers
            const remainingRows = document.querySelectorAll('.csv-row');
            remainingRows.forEach((row, newIndex) => {
                const firstCell = row.querySelector('.csv-cell');
                firstCell.textContent = newIndex + 1;
            });
        }
    }
}

function saveCsvChanges() {
    if (window.adminDashboard) {
        window.adminDashboard.saveCsvChanges();
    }
}

function previewTest() {
    if (window.adminDashboard && window.adminDashboard.currentEditingTestId) {
        const testId = window.adminDashboard.currentEditingTestId;
        window.adminDashboard.showNotification('Preview functionality coming soon!', 'info');
        // TODO: Implement test preview
    }
}

function importQuestions() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
        const file = e.target.files[0];
        if (file && window.adminDashboard) {
            window.adminDashboard.handleTestFileImport(file);
        }
    };
    input.click();
}

function exportQuestions() {
    if (window.adminDashboard && window.adminDashboard.currentEditingTestId) {
        const testId = window.adminDashboard.currentEditingTestId;
        window.adminDashboard.testFileManager.exportTestFile(testId);
    }
}

// Initialize admin dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});

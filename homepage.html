<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Categories - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .homepage-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .homepage-header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .category-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .category-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .category-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .category-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .category-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
            color: #888;
        }
        
        .category-difficulty {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .difficulty-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e0e0e0;
        }
        
        .difficulty-dot.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .quick-stats {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .user-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .user-welcome {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .user-info h3 {
            margin: 0;
            color: #333;
        }
        
        .user-stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .search-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #667eea;
        }
        
        .filter-select {
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            background: white;
            outline: none;
            cursor: pointer;
        }
        
        .no-categories {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-categories-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="homepage-container">
        <!-- Header -->
        <div class="homepage-header">
            <h1>🎯 Adaptive Learning Platform</h1>
            <p>Choose from our comprehensive collection of quiz categories and start your learning journey!</p>
        </div>

        <!-- User Section -->
        <div id="user-section" class="user-section" style="display: none;">
            <div class="user-welcome">
                <div class="user-info">
                    <h3 id="welcome-message">Welcome back!</h3>
                    <div class="user-stats">
                        <span>Best T-Score: <strong id="user-best-score">--</strong></span>
                        <span>Total Quizzes: <strong id="user-total-quizzes">--</strong></span>
                        <span>Last Quiz: <strong id="user-last-quiz">--</strong></span>
                    </div>
                </div>
                <div class="action-buttons">
                    <button id="view-progress-btn" class="btn btn-secondary">View Progress</button>
                    <button id="logout-btn" class="btn btn-outline">Logout</button>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <h3>📊 Platform Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span id="total-categories" class="stat-value">0</span>
                    <span class="stat-label">Categories</span>
                </div>
                <div class="stat-item">
                    <span id="total-questions" class="stat-value">0</span>
                    <span class="stat-label">Questions</span>
                </div>
                <div class="stat-item">
                    <span id="total-users" class="stat-value">0</span>
                    <span class="stat-label">Users</span>
                </div>
                <div class="stat-item">
                    <span id="avg-score" class="stat-value">0%</span>
                    <span class="stat-label">Avg Score</span>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="search-section">
            <h3>🔍 Find Your Quiz</h3>
            <div class="search-bar">
                <input type="text" id="search-input" class="search-input" placeholder="Search categories...">
                <select id="difficulty-filter" class="filter-select">
                    <option value="">All Difficulties</option>
                    <option value="1">Very Easy</option>
                    <option value="2">Easy</option>
                    <option value="3">Medium</option>
                    <option value="4">Hard</option>
                    <option value="5">Very Hard</option>
                </select>
                <button id="random-quiz-btn" class="btn btn-primary">Random Quiz</button>
            </div>
        </div>

        <!-- Categories Grid -->
        <div id="categories-container">
            <h3>📚 Quiz Categories</h3>
            <div id="categories-grid" class="categories-grid">
                <!-- Categories will be loaded here -->
            </div>
            
            <div id="no-categories" class="no-categories" style="display: none;">
                <div class="no-categories-icon">📚</div>
                <h3>No Categories Found</h3>
                <p>No quiz categories match your search criteria.</p>
                <button id="clear-search-btn" class="btn btn-secondary">Clear Search</button>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons" style="justify-content: center; margin-top: 40px;">
            <button id="create-quiz-btn" class="btn btn-primary">Create Custom Quiz</button>
            <button id="admin-panel-btn" class="btn btn-secondary">Admin Panel</button>
            <a href="index.html" class="btn btn-outline">Quick Start Quiz</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/dataManager.js"></script>
    <script src="js/csvQuizEngine.js"></script>
    <script src="js/homepage.js"></script>
</body>
</html>

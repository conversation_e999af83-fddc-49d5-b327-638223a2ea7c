<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .nav-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-header {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .nav-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .nav-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        
        .nav-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .nav-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .nav-features {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .nav-features li {
            color: #888;
            font-size: 14px;
            margin: 5px 0;
        }
        
        .nav-features li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 15px;
        }
        
        .status-ready {
            background: #E8F5E8;
            color: #2E7D32;
        }
        
        .status-new {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .status-admin {
            background: #FFF3E0;
            color: #F57C00;
        }
        
        .quick-links {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .quick-links h3 {
            margin: 0 0 20px 0;
            color: #333;
        }
        
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .quick-link {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .quick-link:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .system-status {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .system-status h3 {
            margin: 0 0 15px 0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .status-item {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="nav-container">
        <!-- Header -->
        <div class="nav-header">
            <h1>🎯 Adaptive Learning Platform</h1>
            <p>Your comprehensive quiz and assessment system</p>
        </div>

        <!-- System Status -->
        <div class="system-status">
            <h3>📊 System Status</h3>
            <p>All systems operational and ready for use</p>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="total-pages">8</div>
                    <div class="status-label">Pages Available</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="total-features">15+</div>
                    <div class="status-label">Features</div>
                </div>
                <div class="status-item">
                    <div class="status-value">✓</div>
                    <div class="status-label">All Links Working</div>
                </div>
                <div class="status-item">
                    <div class="status-value">100%</div>
                    <div class="status-label">Functional</div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="nav-grid">
            <!-- Homepage -->
            <div class="nav-card" onclick="window.location.href='homepage.html'">
                <div class="status-indicator status-ready">Ready</div>
                <span class="nav-icon">🏠</span>
                <h3 class="nav-title">Homepage</h3>
                <p class="nav-description">Browse quiz categories, search for topics, and manage your learning journey.</p>
                <ul class="nav-features">
                    <li>Category browser</li>
                    <li>Search and filter</li>
                    <li>User dashboard</li>
                    <li>Quick statistics</li>
                </ul>
            </div>

            <!-- Main Quiz -->
            <div class="nav-card" onclick="window.location.href='index.html'">
                <div class="status-indicator status-ready">Ready</div>
                <span class="nav-icon">📝</span>
                <h3 class="nav-title">Main Quiz</h3>
                <p class="nav-description">Take adaptive quizzes with T-score analysis and comprehensive results.</p>
                <ul class="nav-features">
                    <li>Adaptive difficulty</li>
                    <li>T-score calculation</li>
                    <li>Random choice order</li>
                    <li>Hint system</li>
                </ul>
            </div>

            <!-- Pre-test -->
            <div class="nav-card" onclick="window.location.href='pretest.html'">
                <div class="status-indicator status-new">New</div>
                <span class="nav-icon">📋</span>
                <h3 class="nav-title">Pre-test Assessment</h3>
                <p class="nav-description">Evaluate your knowledge with fill-in-blank and yes/no questions.</p>
                <ul class="nav-features">
                    <li>Fill-in-blank questions</li>
                    <li>Yes/No questions</li>
                    <li>80% pass threshold</li>
                    <li>Pass/fail qualification</li>
                </ul>
            </div>

            <!-- User Progress -->
            <div class="nav-card" onclick="window.location.href='user-progress.html'">
                <div class="status-indicator status-ready">Ready</div>
                <span class="nav-icon">📈</span>
                <h3 class="nav-title">User Progress</h3>
                <p class="nav-description">Track your learning progress with detailed analytics and achievements.</p>
                <ul class="nav-features">
                    <li>Progress tracking</li>
                    <li>Achievement system</li>
                    <li>Performance charts</li>
                    <li>Recommendations</li>
                </ul>
            </div>

            <!-- Results Pages -->
            <div class="nav-card" onclick="showResultsInfo()">
                <div class="status-indicator status-ready">Ready</div>
                <span class="nav-icon">📊</span>
                <h3 class="nav-title">Results & Analytics</h3>
                <p class="nav-description">Comprehensive result analysis with multiple chart types and insights.</p>
                <ul class="nav-features">
                    <li>T-score visualization</li>
                    <li>Performance analysis</li>
                    <li>Category breakdown</li>
                    <li>Historical progress</li>
                </ul>
            </div>

            <!-- Admin Dashboard -->
            <div class="nav-card" onclick="window.location.href='admin.html'">
                <div class="status-indicator status-admin">Admin</div>
                <span class="nav-icon">⚙️</span>
                <h3 class="nav-title">Admin Dashboard</h3>
                <p class="nav-description">Comprehensive administration with CSV upload and user management.</p>
                <ul class="nav-features">
                    <li>CSV upload & validation</li>
                    <li>User monitoring</li>
                    <li>System settings</li>
                    <li>Data management</li>
                </ul>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <h3>🔗 Quick Links</h3>
            <div class="links-grid">
                <a href="homepage.html" class="quick-link">🏠 Home</a>
                <a href="index.html" class="quick-link">📝 Quick Quiz</a>
                <a href="pretest.html" class="quick-link">📋 Pre-test</a>
                <a href="user-progress.html" class="quick-link">📈 Progress</a>
                <a href="admin.html" class="quick-link">⚙️ Admin</a>
                <a href="#" onclick="showDocumentation()" class="quick-link">📚 Documentation</a>
            </div>
        </div>
    </div>

    <script>
        function showResultsInfo() {
            alert('Results pages are automatically shown after completing quizzes. Take a quiz to see the comprehensive analytics!');
        }
        
        function showDocumentation() {
            alert('Documentation: This platform includes adaptive quizzes, pre-tests, user progress tracking, and admin tools. All features are accessible through the navigation above.');
        }
        
        // Update page count dynamically
        document.addEventListener('DOMContentLoaded', () => {
            const pages = [
                'homepage.html',
                'index.html', 
                'pretest.html',
                'user-progress.html',
                'result.html',
                'pretest-result.html',
                'admin.html',
                'navigation.html'
            ];
            document.getElementById('total-pages').textContent = pages.length;
        });
    </script>
</body>
</html>

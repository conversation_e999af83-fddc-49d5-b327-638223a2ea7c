// Pre-test Application Logic
class PretestApp {
    constructor() {
        this.pretestEngine = new PretestEngine();
        this.currentUser = '';
        this.pretestQuestions = [];
        this.currentQuestionIndex = 0;
        this.startTime = null;
        this.questionStartTime = null;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Welcome screen events
        document.getElementById('start-pretest').addEventListener('click', () => this.startPretest());
        document.getElementById('pretest-username').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.startPretest();
        });

        // Pre-test screen events
        document.getElementById('submit-answer').addEventListener('click', () => this.submitAnswer());
        document.getElementById('next-question').addEventListener('click', () => this.nextQuestion());
        document.getElementById('show-hint').addEventListener('click', () => this.showHint());

        // Answer input events
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('fill-blank-input')) {
                this.checkAnswerInput();
            }
        });

        document.addEventListener('change', (e) => {
            if (e.target.name === 'yes-no') {
                this.checkAnswerInput();
            }
        });
    }

    async startPretest() {
        const usernameInput = document.getElementById('pretest-username');
        this.currentUser = usernameInput.value.trim();
        
        if (!this.currentUser) {
            alert('Please enter your name to start the pre-test.');
            usernameInput.focus();
            return;
        }

        // Store current user
        localStorage.setItem('currentUser', this.currentUser);

        // Show loading screen
        this.showScreen('loading-screen');
        
        try {
            // Load pre-test questions
            await this.pretestEngine.loadQuestions();
            
            // Get mixed questions (fill-blank and yes/no)
            this.pretestQuestions = this.pretestEngine.getMixedQuestions(10);
            this.pretestEngine.setQuestions(this.pretestQuestions);
            
            // Update UI
            document.getElementById('current-user').textContent = this.currentUser;
            document.getElementById('total-questions').textContent = this.pretestQuestions.length;
            
            // Start the pre-test
            this.startTime = new Date();
            this.currentQuestionIndex = 0;
            await this.showQuestion();
            this.showScreen('pretest-screen');
            
        } catch (error) {
            console.error('Error starting pre-test:', error);
            alert('Error loading pre-test questions. Please try again.');
            this.showScreen('welcome-screen');
        }
    }

    async showQuestion() {
        const question = this.pretestQuestions[this.currentQuestionIndex];
        if (!question) {
            this.finishPretest();
            return;
        }

        this.questionStartTime = new Date();
        
        // Update progress
        const progress = ((this.currentQuestionIndex + 1) / this.pretestQuestions.length) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;
        document.getElementById('question-number').textContent = this.currentQuestionIndex + 1;
        
        // Update question type indicator
        const typeIndicator = document.getElementById('question-type');
        if (question.type === 'fill-blank') {
            typeIndicator.textContent = 'Fill in the Blank';
            typeIndicator.className = 'question-type-indicator type-fill-blank';
        } else {
            typeIndicator.textContent = 'Yes / No';
            typeIndicator.className = 'question-type-indicator type-yes-no';
        }
        
        // Show appropriate question interface
        await this.setupQuestionInterface(question);
        
        // Reset UI state
        document.getElementById('submit-answer').disabled = true;
        document.getElementById('next-question').style.display = 'none';
        document.getElementById('answer-feedback').style.display = 'none';
        document.getElementById('hint-section').style.display = 'none';
    }

    async setupQuestionInterface(question) {
        // Hide all containers first
        document.getElementById('fill-blank-container').style.display = 'none';
        document.getElementById('yes-no-container').style.display = 'none';

        if (question.type === 'fill-blank') {
            await this.setupFillBlankQuestion(question);
        } else if (question.type === 'yes-no') {
            await this.setupYesNoQuestion(question);
        }
    }

    async setupFillBlankQuestion(question) {
        const container = document.getElementById('fill-blank-container');
        const questionContainer = document.getElementById('fill-blank-question');

        // Process question text for LaTeX and chemistry
        const processedQuestion = textProcessor.processText(question.question);

        // Replace blank with input field
        const questionWithBlank = processedQuestion.replace(/_{2,}/g,
            '<input type="text" class="fill-blank-input" id="fill-blank-answer" placeholder="Type your answer">'
        );

        questionContainer.innerHTML = questionWithBlank;
        container.style.display = 'block';

        // Render LaTeX if present
        if (textProcessor.containsLatex(question.question) && window.MathJax) {
            try {
                await window.MathJax.typesetPromise([questionContainer]);
            } catch (error) {
                console.warn('MathJax rendering error:', error);
            }
        }

        // Focus on the input
        setTimeout(() => {
            const input = document.getElementById('fill-blank-answer');
            if (input) input.focus();
        }, 100);
    }

    async setupYesNoQuestion(question) {
        const questionElement = document.getElementById('question-text');
        await textProcessor.processAndRender(question.question, questionElement);
        document.getElementById('yes-no-container').style.display = 'flex';

        // Clear previous selections
        document.querySelectorAll('input[name="yes-no"]').forEach(input => {
            input.checked = false;
        });
    }

    checkAnswerInput() {
        const question = this.pretestQuestions[this.currentQuestionIndex];
        let hasAnswer = false;
        
        if (question.type === 'fill-blank') {
            const input = document.getElementById('fill-blank-answer');
            hasAnswer = input && input.value.trim().length > 0;
        } else if (question.type === 'yes-no') {
            hasAnswer = document.querySelector('input[name="yes-no"]:checked') !== null;
        }
        
        document.getElementById('submit-answer').disabled = !hasAnswer;
    }

    submitAnswer() {
        const question = this.pretestQuestions[this.currentQuestionIndex];
        let userAnswer = '';
        
        if (question.type === 'fill-blank') {
            const input = document.getElementById('fill-blank-answer');
            userAnswer = input ? input.value.trim() : '';
        } else if (question.type === 'yes-no') {
            const selected = document.querySelector('input[name="yes-no"]:checked');
            userAnswer = selected ? selected.value : '';
        }
        
        if (!userAnswer) {
            alert('Please provide an answer before submitting.');
            return;
        }
        
        const timeTaken = new Date() - this.questionStartTime;
        const result = this.pretestEngine.submitAnswer(userAnswer, timeTaken);
        
        // Show feedback
        this.showAnswerFeedback(result);
        
        // Disable inputs and show next button
        this.disableInputs();
        document.getElementById('submit-answer').style.display = 'none';
        document.getElementById('next-question').style.display = 'inline-block';
    }

    showAnswerFeedback(result) {
        const feedback = document.getElementById('answer-feedback');
        const feedbackText = document.getElementById('feedback-text');
        
        if (result.isCorrect) {
            feedback.className = 'answer-feedback feedback-correct';
            feedbackText.innerHTML = `
                <strong>✓ Correct!</strong><br>
                Your answer: "${result.userAnswer}"
            `;
        } else {
            feedback.className = 'answer-feedback feedback-incorrect';
            feedbackText.innerHTML = `
                <strong>✗ Incorrect</strong><br>
                Your answer: "${result.userAnswer}"<br>
                Correct answer: "${result.correctAnswer}"
            `;
        }
        
        feedback.style.display = 'block';
    }

    disableInputs() {
        // Disable fill-blank inputs
        document.querySelectorAll('.fill-blank-input').forEach(input => {
            input.disabled = true;
        });
        
        // Disable yes/no inputs
        document.querySelectorAll('input[name="yes-no"]').forEach(input => {
            input.disabled = true;
        });
    }

    async nextQuestion() {
        this.currentQuestionIndex++;
        this.pretestEngine.nextQuestion();

        if (this.currentQuestionIndex < this.pretestQuestions.length) {
            await this.showQuestion();
        } else {
            this.finishPretest();
        }
    }

    showHint() {
        const question = this.pretestQuestions[this.currentQuestionIndex];
        if (question.hint) {
            document.getElementById('hint-text').textContent = question.hint;
            document.getElementById('hint-section').style.display = 'block';
            document.getElementById('show-hint').style.display = 'none';
        }
    }

    async finishPretest() {
        const results = this.pretestEngine.getResults();
        const totalTime = new Date() - this.startTime;
        
        // Add additional data
        const finalResults = {
            ...results,
            username: this.currentUser,
            totalTime: Math.round(totalTime / 1000), // in seconds
            testType: 'pretest'
        };
        
        // Save results to data manager if available
        if (typeof dataManager !== 'undefined' && dataManager.db) {
            try {
                await dataManager.saveResult(finalResults);
                
                // Update user data
                const userData = {
                    username: this.currentUser,
                    lastQuizDate: new Date().toISOString().split('T')[0],
                    lastRawScore: results.rawScore,
                    lastCategory: 'pretest'
                };
                await dataManager.saveUser(userData);
            } catch (error) {
                console.error('Error saving pre-test results:', error);
            }
        }
        
        // Store results in localStorage for the results page
        localStorage.setItem('quizResults', JSON.stringify(finalResults));
        
        // Redirect to results page
        window.location.href = 'pretest-result.html';
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }
}

// Initialize the pre-test app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pretestApp = new PretestApp();
    
    console.log('Pre-test Application loaded successfully!');
    console.log('Questions will be loaded from: data/pretest-questions.csv');
});

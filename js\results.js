// Results Page Logic for CSV Quiz
function initializeResults() {
    // Get results from localStorage
    const resultsData = localStorage.getItem('quizResults');
    
    if (!resultsData) {
        // No results found, redirect to home
        alert('No quiz results found. Please take a quiz first.');
        window.location.href = 'index.html';
        return;
    }
    
    const results = JSON.parse(resultsData);
    displayResults(results);
    setupEventListeners();
}

function displayResults(results) {
    // Update user name
    document.getElementById('user-name').textContent = `Great job, ${results.username}!`;
    
    // Update score summary
    document.getElementById('total-correct').textContent = results.correctAnswers;
    document.getElementById('total-questions').textContent = results.totalQuestions;
    document.getElementById('raw-score').textContent = `${results.rawScore}%`;
    document.getElementById('t-score').textContent = results.tScore;
    
    // Color code T-score
    const tScoreElement = document.getElementById('t-score');
    if (results.tScore >= 70) {
        tScoreElement.className = 'value t-score-value excellent';
    } else if (results.tScore >= 60) {
        tScoreElement.className = 'value t-score-value good';
    } else if (results.tScore >= 50) {
        tScoreElement.className = 'value t-score-value average';
    } else {
        tScoreElement.className = 'value t-score-value below-average';
    }
    
    // Display difficulty breakdown
    displayDifficultyBreakdown(results.difficultyStats);
    
    // Create charts
    createTScoreChart(results);
    createDifficultyJourneyChart(results);
    
    // Display performance message
    displayPerformanceMessage(results);
    
    // Display detailed stats
    displayDetailedStats(results);
}

function displayDifficultyBreakdown(difficultyStats) {
    const container = document.getElementById('difficulty-breakdown');
    container.innerHTML = '';
    
    const difficultyNames = {
        1: 'Very Easy',
        2: 'Easy',
        3: 'Medium',
        4: 'Hard',
        5: 'Very Hard'
    };
    
    Object.entries(difficultyStats).forEach(([level, stats]) => {
        const percentage = stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0;
        
        const difficultyItem = document.createElement('div');
        difficultyItem.className = 'difficulty-item';
        difficultyItem.innerHTML = `
            <div class="difficulty-header">
                <span class="difficulty-name">${difficultyNames[level] || `Level ${level}`}</span>
                <span class="difficulty-score">${stats.correct}/${stats.total} (${percentage}%)</span>
            </div>
            <div class="difficulty-bar">
                <div class="difficulty-progress" style="width: ${percentage}%"></div>
            </div>
        `;
        
        container.appendChild(difficultyItem);
    });
}

function createTScoreChart(results) {
    const ctx = document.getElementById('t-score-chart').getContext('2d');
    
    // Create a gauge-like chart showing T-score
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Your Score', 'Remaining'],
            datasets: [{
                data: [results.tScore, Math.max(0, 100 - results.tScore)],
                backgroundColor: [
                    results.tScore >= 70 ? '#4CAF50' : 
                    results.tScore >= 60 ? '#FF9800' : 
                    results.tScore >= 50 ? '#2196F3' : '#F44336',
                    '#E0E0E0'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.dataIndex === 0) {
                                return `T-Score: ${results.tScore}`;
                            }
                            return '';
                        }
                    }
                }
            }
        },
        plugins: [{
            beforeDraw: function(chart) {
                const width = chart.width;
                const height = chart.height;
                const ctx = chart.ctx;
                
                ctx.restore();
                const fontSize = (height / 114).toFixed(2);
                ctx.font = fontSize + "em sans-serif";
                ctx.textBaseline = "middle";
                ctx.fillStyle = "#333";
                
                const text = results.tScore.toString();
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;
                
                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        }]
    });
}

function createDifficultyJourneyChart(results) {
    const ctx = document.getElementById('difficulty-journey-chart').getContext('2d');
    
    // Extract difficulty progression from answers
    const difficultyProgression = results.answers.map((answer, index) => ({
        question: index + 1,
        difficulty: answer.difficulty,
        correct: answer.isCorrect
    }));
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: difficultyProgression.map(d => `Q${d.question}`),
            datasets: [{
                label: 'Question Difficulty',
                data: difficultyProgression.map(d => d.difficulty),
                borderColor: '#2196F3',
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: 'Correct Answers',
                data: difficultyProgression.map(d => d.correct ? d.difficulty : null),
                borderColor: '#4CAF50',
                backgroundColor: '#4CAF50',
                borderWidth: 0,
                pointRadius: 6,
                pointHoverRadius: 8,
                showLine: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 5,
                    ticks: {
                        stepSize: 1,
                        callback: function(value) {
                            const names = ['', 'Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
                            return names[value] || value;
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const index = context[0].dataIndex;
                            return `Question ${index + 1}`;
                        },
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                const names = ['', 'Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
                                return `Difficulty: ${names[context.parsed.y]}`;
                            } else {
                                return 'Answered Correctly';
                            }
                        }
                    }
                }
            }
        }
    });
}

function displayPerformanceMessage(results) {
    const container = document.getElementById('performance-message');
    let message = '';
    let className = '';
    
    if (results.tScore >= 70) {
        message = `🌟 Excellent performance! Your T-Score of ${results.tScore} places you in the top tier. You've demonstrated exceptional knowledge across multiple difficulty levels.`;
        className = 'excellent';
    } else if (results.tScore >= 60) {
        message = `🎯 Great job! Your T-Score of ${results.tScore} is above average. You're in the qualifying range and show strong understanding of the material.`;
        className = 'good';
    } else if (results.tScore >= 50) {
        message = `📈 Good effort! Your T-Score of ${results.tScore} is around average. With some practice, you can improve your performance significantly.`;
        className = 'average';
    } else {
        message = `💪 Keep practicing! Your T-Score of ${results.tScore} shows room for improvement. Focus on the areas where you struggled and try again.`;
        className = 'below-average';
    }
    
    container.innerHTML = `<div class="message ${className}">${message}</div>`;
}

function displayDetailedStats(results) {
    // Calculate average response time
    const totalTime = results.answers.reduce((sum, answer) => sum + (answer.timeTaken || 0), 0);
    const avgTime = results.answers.length > 0 ? Math.round(totalTime / results.answers.length / 1000) : 0;
    document.getElementById('avg-response-time').textContent = `${avgTime}s`;
    
    // Count hints used (if hint mode was enabled)
    const hintsUsed = results.hintModeUsed ? 'Enabled' : 'Disabled';
    document.getElementById('hints-used').textContent = hintsUsed;
    
    // Calculate difficulty changes
    let difficultyChanges = 0;
    for (let i = 1; i < results.answers.length; i++) {
        if (results.answers[i].difficulty !== results.answers[i-1].difficulty) {
            difficultyChanges++;
        }
    }
    document.getElementById('difficulty-changes').textContent = difficultyChanges;
    
    // Calculate best streak
    let currentStreak = 0;
    let bestStreak = 0;
    results.answers.forEach(answer => {
        if (answer.isCorrect) {
            currentStreak++;
            bestStreak = Math.max(bestStreak, currentStreak);
        } else {
            currentStreak = 0;
        }
    });
    document.getElementById('best-streak').textContent = bestStreak;
}

function setupEventListeners() {
    // Retake quiz button
    document.getElementById('retake-quiz').addEventListener('click', () => {
        localStorage.removeItem('quizResults');
        window.location.href = 'index.html';
    });
    
    // View leaderboard button (placeholder)
    document.getElementById('view-leaderboard').addEventListener('click', () => {
        alert('Leaderboard feature coming soon!');
    });
    
    // Share results button
    document.getElementById('share-results').addEventListener('click', () => {
        const results = JSON.parse(localStorage.getItem('quizResults'));
        const shareText = `I just scored ${results.rawScore}% (T-Score: ${results.tScore}) on the Adaptive Learning Quiz! 🎯`;
        
        if (navigator.share) {
            navigator.share({
                title: 'My Quiz Results',
                text: shareText,
                url: window.location.origin
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                alert('Results copied to clipboard!');
            }).catch(() => {
                alert('Share text: ' + shareText);
            });
        }
    });
    
    // Toggle detailed stats
    document.getElementById('toggle-details').addEventListener('click', () => {
        const content = document.getElementById('detailed-content');
        const button = document.getElementById('toggle-details');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            button.textContent = 'Hide Detailed Statistics';
        } else {
            content.style.display = 'none';
            button.textContent = 'Show Detailed Statistics';
        }
    });
}

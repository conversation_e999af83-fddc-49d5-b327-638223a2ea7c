/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Screen Management */
.screen {
    display: none;
    width: 100%;
}

.screen.active {
    display: block;
}

/* Cards and Containers */
.welcome-card, .login-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.question-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* Typography */
h1 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 2.5em;
}

h2 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.8em;
}

h3 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.3em;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #4a5568;
}

input[type="text"], 
input[type="email"], 
input[type="password"], 
textarea, 
select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea {
    resize: vertical;
    min-height: 80px;
}

/* Toggle Switch */
.toggle-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
}

.toggle-container input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 50px;
    height: 24px;
    background: #cbd5e0;
    border-radius: 12px;
    margin-right: 10px;
    position: relative;
    transition: background 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
}

.toggle-container input:checked + .toggle-slider {
    background: #667eea;
}

.toggle-container input:checked + .toggle-slider::before {
    transform: translateX(26px);
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover {
    background: #4a5568;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-hint {
    background: #ffd700;
    color: #333;
    font-size: 14px;
    padding: 8px 16px;
}

.btn-hint:hover {
    background: #ffed4e;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Quiz Interface */
.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hint-indicator {
    background: #ffd700;
    color: #333;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.progress-info {
    text-align: right;
}

.difficulty-badge {
    background: #e2e8f0;
    color: #4a5568;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 5px;
}

.difficulty-badge.very-easy { background: #c6f6d5; color: #22543d; }
.difficulty-badge.easy { background: #bee3f8; color: #2a4365; }
.difficulty-badge.medium { background: #feebc8; color: #744210; }
.difficulty-badge.hard { background: #fed7d7; color: #742a2a; }
.difficulty-badge.very-hard { background: #e9d8fd; color: #44337a; }

/* Choices */
.choices-container {
    margin: 20px 0;
}

.choice {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.choice:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.choice.selected {
    background: #ebf8ff;
    border-color: #667eea;
    color: #2b6cb0;
}

.choice.correct {
    background: #c6f6d5;
    border-color: #38a169;
    color: #22543d;
}

.choice.incorrect {
    background: #fed7d7;
    border-color: #e53e3e;
    color: #742a2a;
}

.choice-letter {
    background: #667eea;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 15px;
    flex-shrink: 0;
}

/* Hint Container */
.hint-container {
    margin: 15px 0;
    padding: 15px;
    background: #fffbeb;
    border-radius: 8px;
    border-left: 4px solid #ffd700;
}

.hint-text {
    margin-top: 10px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    font-style: italic;
    color: #744210;
}

/* Quiz Footer */
.quiz-footer {
    background: white;
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.score-info {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.score-info span {
    font-weight: 600;
    color: #4a5568;
}

/* Loading Screen */
.loading-content {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255,255,255,0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Page */
.results-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
}

.results-header {
    text-align: center;
    margin-bottom: 30px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.result-card {
    background: #f7fafc;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #e2e8f0;
}

.result-card.full-width {
    grid-column: 1 / -1;
}

.score-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.score-item.highlight {
    background: #ebf8ff;
    padding: 12px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
}

.t-score-value {
    font-size: 1.5em;
    color: #667eea;
}

.chart-container {
    height: 300px;
    margin-bottom: 20px;
}

.t-score-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    align-items: start;
}

.performance-message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: 600;
}

.performance-message.excellent {
    background: #c6f6d5;
    color: #22543d;
    border-left: 4px solid #38a169;
}

.performance-message.good {
    background: #bee3f8;
    color: #2a4365;
    border-left: 4px solid #3182ce;
}

.performance-message.average {
    background: #feebc8;
    color: #744210;
    border-left: 4px solid #dd6b20;
}

.results-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

/* Admin Dashboard */
.admin-container {
    align-items: flex-start;
    padding-top: 20px;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.admin-actions {
    display: flex;
    gap: 10px;
}

.admin-tabs {
    display: flex;
    background: white;
    border-radius: 15px;
    padding: 5px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    color: #718096;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-content {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tab-content.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-top: 10px;
}

.overview-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.chart-card {
    background: #f7fafc;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #e2e8f0;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

/* Tables */
.search-filter {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.search-filter input,
.search-filter select {
    flex: 1;
    max-width: 200px;
}

.table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
}

tr:hover {
    background: #f7fafc;
}

.action-btn {
    padding: 6px 12px;
    font-size: 12px;
    margin-right: 5px;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Detailed Stats */
.detailed-stats {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #e2e8f0;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    text-decoration: underline;
    cursor: pointer;
    font-size: 16px;
}

.detailed-content {
    margin-top: 15px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-label {
    font-weight: 600;
    color: #4a5568;
}

.stat-value {
    color: #667eea;
    font-weight: bold;
}

/* Difficulty Breakdown */
.difficulty-breakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.difficulty-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.difficulty-label {
    font-weight: 600;
}

.difficulty-score {
    color: #667eea;
    font-weight: bold;
}

/* Journey Description */
.journey-container {
    height: 250px;
    margin-bottom: 15px;
}

.journey-description {
    color: #718096;
    font-style: italic;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .welcome-card, .login-card {
        padding: 20px;
    }

    .quiz-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .t-score-analysis {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    .admin-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .tab-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .overview-charts {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .search-filter {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filter input,
    .search-filter select {
        max-width: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .difficulty-breakdown {
        gap: 10px;
    }

    .difficulty-item {
        padding: 12px;
    }

    .performance-message .message {
        padding: 15px;
        font-size: 14px;
    }
}

/* CSV Quiz Specific Styles */
.difficulty-breakdown {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.difficulty-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.difficulty-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.difficulty-name {
    font-weight: 600;
    color: #333;
}

.difficulty-score {
    font-weight: 500;
    color: #666;
}

.difficulty-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.difficulty-progress {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* T-Score Styling */
.t-score-value.excellent {
    color: #4CAF50;
    font-weight: bold;
}

.t-score-value.good {
    color: #FF9800;
    font-weight: bold;
}

.t-score-value.average {
    color: #2196F3;
    font-weight: bold;
}

.t-score-value.below-average {
    color: #F44336;
    font-weight: bold;
}

/* Performance Messages */
.performance-message .message {
    padding: 20px;
    border-radius: 10px;
    font-size: 16px;
    line-height: 1.6;
}

.performance-message .message.excellent {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.performance-message .message.good {
    background: linear-gradient(135deg, #FF9800, #f57c00);
    color: white;
}

.performance-message .message.average {
    background: linear-gradient(135deg, #2196F3, #1976d2);
    color: white;
}

.performance-message .message.below-average {
    background: linear-gradient(135deg, #F44336, #d32f2f);
    color: white;
}

/* Choice Feedback */
.choice-label.correct {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
    border-color: #4CAF50 !important;
}

.choice-label.incorrect {
    background: linear-gradient(135deg, #F44336, #d32f2f) !important;
    color: white !important;
    border-color: #F44336 !important;
}

/* Difficulty Badges */
.difficulty-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-badge.difficulty-1 {
    background: #E8F5E8;
    color: #2E7D32;
}

.difficulty-badge.difficulty-2 {
    background: #FFF3E0;
    color: #F57C00;
}

.difficulty-badge.difficulty-3 {
    background: #E3F2FD;
    color: #1976D2;
}

.difficulty-badge.difficulty-4 {
    background: #FCE4EC;
    color: #C2185B;
}

.difficulty-badge.difficulty-5 {
    background: #FFEBEE;
    color: #D32F2F;
}

/* Loading Animation for CSV */
.csv-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
}

.csv-loading::before {
    content: "📊";
    font-size: 24px;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Admin Dashboard Specific Styles */
.upload-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
    margin-bottom: 30px;
}

.upload-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover, .upload-area.drag-over {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.upload-options {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.upload-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.upload-status {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.upload-progress {
    margin-top: 15px;
}

.upload-progress .progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.upload-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.csv-preview {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.preview-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.preview-stats span {
    font-size: 14px;
    color: #666;
}

.preview-stats strong {
    color: #333;
}

.preview-actions {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.csv-validation {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.validation-errors {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 15px;
}

.validation-errors h4 {
    color: #c53030;
    margin-bottom: 10px;
}

.validation-errors ul {
    margin: 0;
    padding-left: 20px;
}

.validation-errors li {
    color: #742a2a;
    margin-bottom: 5px;
}

.data-management {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.management-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

.management-card h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.management-card p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.settings-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.settings-card h3 {
    margin: 0 0 20px 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.maintenance-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.system-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.system-info h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.system-info p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.invalid-row {
    background: #fff5f5 !important;
    color: #c53030;
}

.table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.btn-danger {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c);
}

/* Enhanced Results Page Styles */
.analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-top: 20px;
}

.stats-charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-top: 20px;
}

.chart-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    position: relative;
}

.chart-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    text-align: center;
}

.chart-section canvas {
    max-height: 300px;
}

.journey-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

.journey-description {
    text-align: center;
    color: #666;
    font-style: italic;
    margin-top: 15px;
}

/* Responsive adjustments for charts */
@media (max-width: 768px) {
    .analysis-grid,
    .comparison-grid {
        grid-template-columns: 1fr;
    }

    .stats-charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-section {
        padding: 15px;
    }

    .chart-section canvas {
        max-height: 250px;
    }

    .journey-container {
        height: 300px;
    }
}

/* Admin Modal Styles */
.large-modal {
    max-width: 800px;
    width: 90%;
}

.choices-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 15px 0;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.question-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #667eea;
}

.question-preview h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.choices-preview {
    margin: 10px 0;
}

.choice-preview {
    padding: 5px 0;
    color: #666;
}

.choice-preview.correct {
    color: #4CAF50;
    font-weight: bold;
}

@media (max-width: 768px) {
    .choices-grid {
        grid-template-columns: 1fr;
    }

    .large-modal {
        width: 95%;
        padding: 20px;
    }

    .modal-actions {
        flex-direction: column;
    }
}

/* Test File Management Styles */
.test-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.test-file-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}

.test-file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.test-file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.test-file-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.test-file-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.test-file-status.active {
    background: #c6f6d5;
    color: #22543d;
}

.test-file-status.inactive {
    background: #fed7d7;
    color: #742a2a;
}

.test-file-status.draft {
    background: #feebc8;
    color: #744210;
}

.test-file-info {
    margin: 15px 0;
}

.test-file-info p {
    margin: 8px 0;
    color: #666;
    font-size: 14px;
}

.test-file-info strong {
    color: #333;
}

.test-file-actions {
    display: flex;
    gap: 8px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.add-new-card {
    border: 2px dashed #ccc;
    background: #f9f9f9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.add-new-card:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.add-new-content {
    text-align: center;
    color: #666;
}

.add-new-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.add-new-content h3 {
    margin: 10px 0;
    color: #333;
}

.add-new-content p {
    margin: 0;
    font-size: 14px;
}

.test-stats-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.test-stats-section h3 {
    margin: 0 0 20px 0;
    color: #333;
}

/* CSV Editor Styles */
.extra-large-modal {
    max-width: 95%;
    width: 95%;
    max-height: 90vh;
}

.csv-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.toolbar-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.csv-editor-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
}

.csv-editor-header {
    display: grid;
    grid-template-columns: 50px 2fr 1fr 1fr 1fr 1fr 80px 1fr 80px 1fr 100px;
    background: #f8f9fa;
    border-bottom: 2px solid #e0e0e0;
    font-weight: 600;
    color: #333;
}

.csv-column {
    padding: 12px 8px;
    border-right: 1px solid #e0e0e0;
    font-size: 12px;
    text-align: center;
}

.csv-editor-body {
    max-height: 350px;
    overflow-y: auto;
}

.csv-row {
    display: grid;
    grid-template-columns: 50px 2fr 1fr 1fr 1fr 1fr 80px 1fr 80px 1fr 100px;
    border-bottom: 1px solid #e0e0e0;
}

.csv-row:hover {
    background: #f8f9fa;
}

.csv-cell {
    padding: 8px;
    border-right: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
}

.csv-cell input,
.csv-cell textarea,
.csv-cell select {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 12px;
    padding: 4px;
}

.csv-cell textarea {
    resize: none;
    min-height: 40px;
}

.csv-cell input:focus,
.csv-cell textarea:focus,
.csv-cell select:focus {
    background: white;
    border: 1px solid #667eea;
    border-radius: 3px;
}

.csv-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.csv-actions button {
    padding: 4px 8px;
    font-size: 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.btn-edit {
    background: #667eea;
    color: white;
}

.btn-delete {
    background: #e53e3e;
    color: white;
}

.btn-duplicate {
    background: #38a169;
    color: white;
}

@media (max-width: 768px) {
    .test-files-grid {
        grid-template-columns: 1fr;
    }

    .extra-large-modal {
        width: 98%;
        max-width: 98%;
    }

    .csv-editor-header,
    .csv-row {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(11, auto);
    }

    .csv-column,
    .csv-cell {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .toolbar-section {
        flex-direction: column;
        gap: 5px;
    }
}

// Data Management System for Quiz Application
class DataManager {
    constructor() {
        this.dbName = 'QuizAppDB';
        this.version = 1;
        this.db = null;
        this.initializeDB();
    }

    // Initialize IndexedDB for better data storage
    async initializeDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Users store
                if (!db.objectStoreNames.contains('users')) {
                    const userStore = db.createObjectStore('users', { keyPath: 'username' });
                    userStore.createIndex('lastQuizDate', 'lastQuizDate', { unique: false });
                    userStore.createIndex('bestTScore', 'bestTScore', { unique: false });
                }
                
                // Quiz results store
                if (!db.objectStoreNames.contains('results')) {
                    const resultStore = db.createObjectStore('results', { keyPath: 'id', autoIncrement: true });
                    resultStore.createIndex('username', 'username', { unique: false });
                    resultStore.createIndex('timestamp', 'timestamp', { unique: false });
                    resultStore.createIndex('tScore', 'tScore', { unique: false });
                    resultStore.createIndex('category', 'category', { unique: false });
                }
                
                // Quiz categories store
                if (!db.objectStoreNames.contains('categories')) {
                    const categoryStore = db.createObjectStore('categories', { keyPath: 'id', autoIncrement: true });
                    categoryStore.createIndex('name', 'name', { unique: true });
                }
                
                // Questions store (for uploaded CSVs)
                if (!db.objectStoreNames.contains('questions')) {
                    const questionStore = db.createObjectStore('questions', { keyPath: 'id' });
                    questionStore.createIndex('category', 'category', { unique: false });
                    questionStore.createIndex('difficulty', 'difficulty', { unique: false });
                }
                
                // T-score statistics store
                if (!db.objectStoreNames.contains('tscoreStats')) {
                    const tscoreStore = db.createObjectStore('tscoreStats', { keyPath: 'id' });
                }
            };
        });
    }

    // User Management
    async saveUser(userData) {
        const transaction = this.db.transaction(['users'], 'readwrite');
        const store = transaction.objectStore('users');
        
        const existingUser = await this.getUser(userData.username);
        if (existingUser) {
            // Update existing user
            userData.totalAttempts = (existingUser.totalAttempts || 0) + 1;
            userData.bestTScore = Math.max(existingUser.bestTScore || 0, userData.bestTScore || 0);
            userData.firstQuizDate = existingUser.firstQuizDate || userData.lastQuizDate;
        } else {
            // New user
            userData.totalAttempts = 1;
            userData.firstQuizDate = userData.lastQuizDate;
        }
        
        return store.put(userData);
    }

    async getUser(username) {
        const transaction = this.db.transaction(['users'], 'readonly');
        const store = transaction.objectStore('users');
        return new Promise((resolve, reject) => {
            const request = store.get(username);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllUsers() {
        const transaction = this.db.transaction(['users'], 'readonly');
        const store = transaction.objectStore('users');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Results Management
    async saveResult(resultData) {
        const transaction = this.db.transaction(['results'], 'readwrite');
        const store = transaction.objectStore('results');
        
        resultData.timestamp = new Date().toISOString();
        resultData.id = Date.now() + Math.random(); // Simple ID generation
        
        return store.add(resultData);
    }

    async getUserResults(username) {
        const transaction = this.db.transaction(['results'], 'readonly');
        const store = transaction.objectStore('results');
        const index = store.index('username');
        
        return new Promise((resolve, reject) => {
            const request = index.getAll(username);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllResults() {
        const transaction = this.db.transaction(['results'], 'readonly');
        const store = transaction.objectStore('results');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // T-Score Management
    async calculateTScore(rawScore, category = 'general') {
        const allResults = await this.getAllResults();
        const categoryResults = allResults.filter(r => r.category === category || category === 'general');
        
        if (categoryResults.length < 5) {
            // Not enough data, use default values
            const populationMean = 47; // Slightly below average for new systems
            const populationStdDev = 12;
            const tScore = 50 + 10 * ((rawScore - populationMean) / populationStdDev);
            return {
                tScore: Math.max(20, Math.min(80, Math.round(tScore * 10) / 10)),
                populationMean,
                populationStdDev,
                sampleSize: categoryResults.length,
                percentile: this.calculatePercentile(tScore)
            };
        }
        
        // Calculate actual population statistics
        const scores = categoryResults.map(r => r.rawScore);
        const populationMean = scores.reduce((a, b) => a + b, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - populationMean, 2), 0) / scores.length;
        const populationStdDev = Math.sqrt(variance);
        
        // Calculate T-score
        const tScore = 50 + 10 * ((rawScore - populationMean) / populationStdDev);
        const boundedTScore = Math.max(20, Math.min(80, Math.round(tScore * 10) / 10));
        
        // Save updated statistics
        await this.saveTScoreStats(category, {
            populationMean,
            populationStdDev,
            sampleSize: categoryResults.length,
            lastUpdated: new Date().toISOString()
        });
        
        return {
            tScore: boundedTScore,
            populationMean: Math.round(populationMean * 10) / 10,
            populationStdDev: Math.round(populationStdDev * 10) / 10,
            sampleSize: categoryResults.length,
            percentile: this.calculatePercentile(boundedTScore)
        };
    }

    calculatePercentile(tScore) {
        // Convert T-score to percentile (approximate)
        const z = (tScore - 50) / 10;
        const percentile = this.normalCDF(z) * 100;
        return Math.round(Math.max(1, Math.min(99, percentile)));
    }

    normalCDF(x) {
        // Approximation of normal cumulative distribution function
        return 0.5 * (1 + this.erf(x / Math.sqrt(2)));
    }

    erf(x) {
        // Approximation of error function
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;
        
        const sign = x >= 0 ? 1 : -1;
        x = Math.abs(x);
        
        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
        
        return sign * y;
    }

    async saveTScoreStats(category, stats) {
        const transaction = this.db.transaction(['tscoreStats'], 'readwrite');
        const store = transaction.objectStore('tscoreStats');
        stats.id = category;
        return store.put(stats);
    }

    async getTScoreStats(category = 'general') {
        const transaction = this.db.transaction(['tscoreStats'], 'readonly');
        const store = transaction.objectStore('tscoreStats');
        return new Promise((resolve, reject) => {
            const request = store.get(category);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Category Management
    async saveCategory(categoryData) {
        const transaction = this.db.transaction(['categories'], 'readwrite');
        const store = transaction.objectStore('categories');
        return store.add(categoryData);
    }

    async getAllCategories() {
        const transaction = this.db.transaction(['categories'], 'readonly');
        const store = transaction.objectStore('categories');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Question Management
    async saveQuestions(questions) {
        const transaction = this.db.transaction(['questions'], 'readwrite');
        const store = transaction.objectStore('questions');
        
        const promises = questions.map(question => store.put(question));
        return Promise.all(promises);
    }

    async getQuestionsByCategory(category) {
        const transaction = this.db.transaction(['questions'], 'readonly');
        const store = transaction.objectStore('questions');
        const index = store.index('category');
        
        return new Promise((resolve, reject) => {
            const request = index.getAll(category);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllQuestions() {
        const transaction = this.db.transaction(['questions'], 'readonly');
        const store = transaction.objectStore('questions');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Analytics and Statistics
    async getAnalytics() {
        const [users, results, categories] = await Promise.all([
            this.getAllUsers(),
            this.getAllResults(),
            this.getAllCategories()
        ]);

        const analytics = {
            totalUsers: users.length,
            totalQuizzes: results.length,
            totalCategories: categories.length,
            averageTScore: results.length > 0 ? 
                results.reduce((sum, r) => sum + (r.tScore || 0), 0) / results.length : 0,
            userGrowth: this.calculateUserGrowth(users),
            categoryStats: this.calculateCategoryStats(results),
            difficultyStats: this.calculateDifficultyStats(results),
            recentActivity: this.getRecentActivity(results, 7)
        };

        return analytics;
    }

    calculateUserGrowth(users) {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        const newUsersLast30Days = users.filter(u => 
            new Date(u.firstQuizDate) >= thirtyDaysAgo
        ).length;
        
        const newUsersLast7Days = users.filter(u => 
            new Date(u.firstQuizDate) >= sevenDaysAgo
        ).length;
        
        return {
            last30Days: newUsersLast30Days,
            last7Days: newUsersLast7Days
        };
    }

    calculateCategoryStats(results) {
        const categoryStats = {};
        results.forEach(result => {
            const category = result.category || 'General';
            if (!categoryStats[category]) {
                categoryStats[category] = {
                    totalAttempts: 0,
                    averageScore: 0,
                    averageTScore: 0
                };
            }
            categoryStats[category].totalAttempts++;
            categoryStats[category].averageScore += result.rawScore || 0;
            categoryStats[category].averageTScore += result.tScore || 0;
        });
        
        Object.keys(categoryStats).forEach(category => {
            const stats = categoryStats[category];
            stats.averageScore = Math.round((stats.averageScore / stats.totalAttempts) * 10) / 10;
            stats.averageTScore = Math.round((stats.averageTScore / stats.totalAttempts) * 10) / 10;
        });
        
        return categoryStats;
    }

    calculateDifficultyStats(results) {
        const difficultyStats = {};
        results.forEach(result => {
            if (result.difficultyStats) {
                Object.entries(result.difficultyStats).forEach(([level, stats]) => {
                    if (!difficultyStats[level]) {
                        difficultyStats[level] = { total: 0, correct: 0 };
                    }
                    difficultyStats[level].total += stats.total;
                    difficultyStats[level].correct += stats.correct;
                });
            }
        });
        
        return difficultyStats;
    }

    getRecentActivity(results, days) {
        const now = new Date();
        const cutoffDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
        
        return results
            .filter(r => new Date(r.timestamp) >= cutoffDate)
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, 20);
    }

    // Utility method to clear all data (for testing)
    async clearAllData() {
        const stores = ['users', 'results', 'categories', 'questions', 'tscoreStats'];
        const transaction = this.db.transaction(stores, 'readwrite');
        
        const promises = stores.map(storeName => {
            const store = transaction.objectStore(storeName);
            return store.clear();
        });
        
        return Promise.all(promises);
    }
}

// Create global instance
const dataManager = new DataManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataManager;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container admin-container">
        <!-- Login Screen -->
        <div id="admin-login" class="screen active">
            <div class="login-card">
                <h1>🔐 Admin Login</h1>
                <form id="login-form">
                    <div class="form-group">
                        <label for="admin-username">Username:</label>
                        <input type="text" id="admin-username" required>
                    </div>
                    <div class="form-group">
                        <label for="admin-password">Password:</label>
                        <input type="password" id="admin-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </div>

        <!-- Admin Dashboard -->
        <div id="admin-dashboard" class="screen">
            <div class="admin-header">
                <h1>📊 Admin Dashboard</h1>
                <div class="admin-actions">
                    <button id="refresh-data" class="btn btn-secondary">🔄 Refresh</button>
                    <button id="logout" class="btn btn-outline">Logout</button>
                </div>
            </div>

            <div class="admin-tabs">
                <button class="tab-btn active" data-tab="overview">Overview</button>
                <button class="tab-btn" data-tab="users">Users</button>
                <button class="tab-btn" data-tab="questions">Questions</button>
                <button class="tab-btn" data-tab="analytics">Analytics</button>
            </div>

            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <div class="stat-number" id="total-users">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Questions</h3>
                        <div class="stat-number" id="total-questions-admin">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Average T-Score</h3>
                        <div class="stat-number" id="avg-t-score">50.0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Quiz Attempts</h3>
                        <div class="stat-number" id="total-attempts">0</div>
                    </div>
                </div>

                <div class="overview-charts">
                    <div class="chart-card">
                        <h3>T-Score Distribution</h3>
                        <canvas id="t-score-distribution"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>Daily Quiz Activity</h3>
                        <canvas id="daily-activity"></canvas>
                    </div>
                </div>
            </div>

            <!-- Users Tab -->
            <div id="users-tab" class="tab-content">
                <div class="tab-header">
                    <h2>User Management</h2>
                    <div class="tab-actions">
                        <button id="export-users" class="btn btn-secondary">📥 Export CSV</button>
                        <button id="add-user" class="btn btn-primary">➕ Add User</button>
                    </div>
                </div>
                
                <div class="search-filter">
                    <input type="text" id="user-search" placeholder="Search users...">
                    <select id="user-filter">
                        <option value="all">All Users</option>
                        <option value="high">High Performers (T ≥ 60)</option>
                        <option value="average">Average Performers (T 40-60)</option>
                        <option value="low">Low Performers (T < 40)</option>
                    </select>
                </div>

                <div class="table-container">
                    <table id="users-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Last Quiz</th>
                                <th>Best T-Score</th>
                                <th>Attempts</th>
                                <th>Avg Accuracy</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- User data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Questions Tab -->
            <div id="questions-tab" class="tab-content">
                <div class="tab-header">
                    <h2>Question Management</h2>
                    <div class="tab-actions">
                        <button id="import-questions" class="btn btn-secondary">📤 Import from Sheet</button>
                        <button id="add-question" class="btn btn-primary">➕ Add Question</button>
                    </div>
                </div>

                <div class="search-filter">
                    <input type="text" id="question-search" placeholder="Search questions...">
                    <select id="difficulty-filter">
                        <option value="all">All Difficulties</option>
                        <option value="1">Very Easy</option>
                        <option value="2">Easy</option>
                        <option value="3">Medium</option>
                        <option value="4">Hard</option>
                        <option value="5">Very Hard</option>
                    </select>
                    <select id="category-filter">
                        <option value="all">All Categories</option>
                    </select>
                </div>

                <div class="table-container">
                    <table id="questions-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Question</th>
                                <th>Difficulty</th>
                                <th>Category</th>
                                <th>Usage Count</th>
                                <th>Success Rate</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Question data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div id="analytics-tab" class="tab-content">
                <div class="analytics-grid">
                    <div class="chart-card">
                        <h3>Performance by Difficulty</h3>
                        <canvas id="difficulty-performance"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>Question Category Analysis</h3>
                        <canvas id="category-analysis"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>User Progress Over Time</h3>
                        <canvas id="progress-timeline"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>Hint Usage Statistics</h3>
                        <canvas id="hint-usage"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modals -->
        <div id="user-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2 id="user-modal-title">Add User</h2>
                <form id="user-form">
                    <div class="form-group">
                        <label for="user-name">Name:</label>
                        <input type="text" id="user-name" required>
                    </div>
                    <div class="form-group">
                        <label for="user-email">Email:</label>
                        <input type="email" id="user-email">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('user-modal')">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="question-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2 id="question-modal-title">Add Question</h2>
                <form id="question-form">
                    <div class="form-group">
                        <label for="question-text">Question:</label>
                        <textarea id="question-text" required></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="choice-a">Choice A:</label>
                            <input type="text" id="choice-a" required>
                        </div>
                        <div class="form-group">
                            <label for="choice-b">Choice B:</label>
                            <input type="text" id="choice-b" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="choice-c">Choice C:</label>
                            <input type="text" id="choice-c" required>
                        </div>
                        <div class="form-group">
                            <label for="choice-d">Choice D:</label>
                            <input type="text" id="choice-d" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="correct-answer">Correct Answer:</label>
                            <select id="correct-answer" required>
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                                <option value="D">D</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="question-difficulty">Difficulty:</label>
                            <select id="question-difficulty" required>
                                <option value="1">Very Easy</option>
                                <option value="2">Easy</option>
                                <option value="3">Medium</option>
                                <option value="4">Hard</option>
                                <option value="5">Very Hard</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="question-category">Category:</label>
                        <input type="text" id="question-category" required>
                    </div>
                    <div class="form-group">
                        <label for="question-hint">Hint (optional):</label>
                        <textarea id="question-hint"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('question-modal')">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script>
        // Admin functionality will be implemented here
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdmin();
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container admin-container">
        <!-- Login Screen -->
        <div id="admin-login" class="screen active">
            <div class="login-card">
                <h1>🔐 Admin Login</h1>
                <form id="login-form">
                    <div class="form-group">
                        <label for="admin-username">Username:</label>
                        <input type="text" id="admin-username" required>
                    </div>
                    <div class="form-group">
                        <label for="admin-password">Password:</label>
                        <input type="password" id="admin-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </div>

        <!-- Admin Dashboard -->
        <div id="admin-dashboard" class="screen">
            <div class="admin-header">
                <h1>📊 Admin Dashboard</h1>
                <div class="admin-actions">
                    <button id="refresh-data" class="btn btn-secondary">🔄 Refresh</button>
                    <button id="logout" class="btn btn-outline">Logout</button>
                </div>
            </div>

            <div class="admin-tabs">
                <button class="tab-btn active" data-tab="overview">Overview</button>
                <button class="tab-btn" data-tab="users">Users</button>
                <button class="tab-btn" data-tab="questions">Questions</button>
                <button class="tab-btn" data-tab="csv-upload">CSV Upload</button>
                <button class="tab-btn" data-tab="analytics">Analytics</button>
                <button class="tab-btn" data-tab="settings">Settings</button>
            </div>

            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <div class="stat-number" id="total-users">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Questions</h3>
                        <div class="stat-number" id="total-questions-admin">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Average T-Score</h3>
                        <div class="stat-number" id="avg-t-score">50.0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Quiz Attempts</h3>
                        <div class="stat-number" id="total-attempts">0</div>
                    </div>
                </div>

                <div class="overview-charts">
                    <div class="chart-card">
                        <h3>T-Score Distribution</h3>
                        <canvas id="t-score-distribution"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>Daily Quiz Activity</h3>
                        <canvas id="daily-activity"></canvas>
                    </div>
                </div>
            </div>

            <!-- Users Tab -->
            <div id="users-tab" class="tab-content">
                <div class="tab-header">
                    <h2>User Management</h2>
                    <div class="tab-actions">
                        <button id="export-users" class="btn btn-secondary">📥 Export CSV</button>
                        <button id="add-user" class="btn btn-primary">➕ Add User</button>
                    </div>
                </div>
                
                <div class="search-filter">
                    <input type="text" id="user-search" placeholder="Search users...">
                    <select id="user-filter">
                        <option value="all">All Users</option>
                        <option value="high">High Performers (T ≥ 60)</option>
                        <option value="average">Average Performers (T 40-60)</option>
                        <option value="low">Low Performers (T < 40)</option>
                    </select>
                </div>

                <div class="table-container">
                    <table id="users-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Last Quiz</th>
                                <th>Best T-Score</th>
                                <th>Attempts</th>
                                <th>Avg Accuracy</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- User data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Questions Tab -->
            <div id="questions-tab" class="tab-content">
                <div class="tab-header">
                    <h2>Question Management</h2>
                    <div class="tab-actions">
                        <button id="import-questions" class="btn btn-secondary">📤 Import from Sheet</button>
                        <button id="add-question" class="btn btn-primary">➕ Add Question</button>
                    </div>
                </div>

                <div class="search-filter">
                    <input type="text" id="question-search" placeholder="Search questions...">
                    <select id="difficulty-filter">
                        <option value="all">All Difficulties</option>
                        <option value="1">Very Easy</option>
                        <option value="2">Easy</option>
                        <option value="3">Medium</option>
                        <option value="4">Hard</option>
                        <option value="5">Very Hard</option>
                    </select>
                    <select id="category-filter">
                        <option value="all">All Categories</option>
                    </select>
                </div>

                <div class="table-container">
                    <table id="questions-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Question</th>
                                <th>Difficulty</th>
                                <th>Category</th>
                                <th>Usage Count</th>
                                <th>Success Rate</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Question data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- CSV Upload Tab -->
            <div id="csv-upload-tab" class="tab-content">
                <div class="tab-header">
                    <h2>📤 CSV Upload & Management</h2>
                    <div class="tab-actions">
                        <button id="download-template" class="btn btn-secondary">📥 Download Template</button>
                        <button id="backup-data" class="btn btn-outline">💾 Backup Data</button>
                    </div>
                </div>

                <div class="upload-section">
                    <div class="upload-card">
                        <h3>📋 Upload Questions CSV</h3>
                        <div class="upload-area" id="csv-upload-area">
                            <div class="upload-icon">📁</div>
                            <p>Drag and drop your CSV file here, or click to browse</p>
                            <input type="file" id="csv-file-input" accept=".csv" style="display: none;">
                            <button id="browse-csv" class="btn btn-primary">Browse Files</button>
                        </div>

                        <div class="upload-options">
                            <label>
                                <input type="checkbox" id="replace-existing" checked>
                                Replace existing questions
                            </label>
                            <label>
                                <input type="checkbox" id="validate-before-upload" checked>
                                Validate before upload
                            </label>
                        </div>
                    </div>

                    <div class="upload-card">
                        <h3>📊 Upload Status</h3>
                        <div id="upload-status" class="upload-status">
                            <p>No file selected</p>
                        </div>
                        <div id="upload-progress" class="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="upload-progress-bar"></div>
                            </div>
                            <p id="upload-progress-text">0%</p>
                        </div>
                    </div>
                </div>

                <div class="csv-preview" id="csv-preview" style="display: none;">
                    <h3>📋 CSV Preview</h3>
                    <div class="preview-stats">
                        <span>Total Rows: <strong id="preview-total">0</strong></span>
                        <span>Valid Questions: <strong id="preview-valid">0</strong></span>
                        <span>Errors: <strong id="preview-errors">0</strong></span>
                    </div>
                    <div class="table-container">
                        <table id="csv-preview-table">
                            <thead id="csv-preview-header"></thead>
                            <tbody id="csv-preview-body"></tbody>
                        </table>
                    </div>
                    <div class="preview-actions">
                        <button id="confirm-upload" class="btn btn-primary">✅ Confirm Upload</button>
                        <button id="cancel-upload" class="btn btn-secondary">❌ Cancel</button>
                    </div>
                </div>

                <div class="csv-validation" id="csv-validation" style="display: none;">
                    <h3>⚠️ Validation Results</h3>
                    <div id="validation-results"></div>
                </div>

                <div class="data-management">
                    <h3>🗄️ Data Management</h3>
                    <div class="management-grid">
                        <div class="management-card">
                            <h4>Questions Database</h4>
                            <p>Total Questions: <span id="total-questions-count">0</span></p>
                            <p>Categories: <span id="total-categories-count">0</span></p>
                            <button id="export-questions" class="btn btn-secondary">Export Questions</button>
                        </div>
                        <div class="management-card">
                            <h4>User Data</h4>
                            <p>Total Users: <span id="total-users-count">0</span></p>
                            <p>Total Results: <span id="total-results-count">0</span></p>
                            <button id="export-results" class="btn btn-secondary">Export Results</button>
                        </div>
                        <div class="management-card">
                            <h4>System Actions</h4>
                            <button id="clear-cache" class="btn btn-outline">Clear Cache</button>
                            <button id="reset-tscores" class="btn btn-outline">Reset T-Scores</button>
                            <button id="clear-all-data" class="btn btn-danger">⚠️ Clear All Data</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div id="analytics-tab" class="tab-content">
                <div class="analytics-grid">
                    <div class="chart-card">
                        <h3>Performance by Difficulty</h3>
                        <canvas id="difficulty-performance"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>Question Category Analysis</h3>
                        <canvas id="category-analysis"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>User Progress Over Time</h3>
                        <canvas id="progress-timeline"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>Hint Usage Statistics</h3>
                        <canvas id="hint-usage"></canvas>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="tab-header">
                    <h2>⚙️ System Settings</h2>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>🎯 T-Score Configuration</h3>
                        <div class="form-group">
                            <label for="default-mean">Default Population Mean:</label>
                            <input type="number" id="default-mean" value="47" min="0" max="100">
                            <small>Used when insufficient data is available</small>
                        </div>
                        <div class="form-group">
                            <label for="default-stddev">Default Standard Deviation:</label>
                            <input type="number" id="default-stddev" value="12" min="1" max="50">
                            <small>Used when insufficient data is available</small>
                        </div>
                        <div class="form-group">
                            <label for="min-sample-size">Minimum Sample Size:</label>
                            <input type="number" id="min-sample-size" value="5" min="1" max="100">
                            <small>Minimum results needed for dynamic T-score calculation</small>
                        </div>
                        <button id="save-tscore-settings" class="btn btn-primary">Save T-Score Settings</button>
                    </div>

                    <div class="settings-card">
                        <h3>📊 Quiz Configuration</h3>
                        <div class="form-group">
                            <label for="default-quiz-length">Default Quiz Length:</label>
                            <input type="number" id="default-quiz-length" value="20" min="5" max="100">
                        </div>
                        <div class="form-group">
                            <label for="passing-score">Passing Score (%):</label>
                            <input type="number" id="passing-score" value="70" min="0" max="100">
                        </div>
                        <div class="form-group">
                            <label for="excellence-threshold">Excellence T-Score Threshold:</label>
                            <input type="number" id="excellence-threshold" value="70" min="50" max="100">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable-hints" checked>
                                Enable Hint System
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable-adaptive" checked>
                                Enable Adaptive Difficulty
                            </label>
                        </div>
                        <button id="save-quiz-settings" class="btn btn-primary">Save Quiz Settings</button>
                    </div>

                    <div class="settings-card">
                        <h3>👥 User Management</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="allow-guest-users" checked>
                                Allow Guest Users
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="require-email" checked>
                                Require Email for Registration
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="max-attempts-per-day">Max Quiz Attempts Per Day:</label>
                            <input type="number" id="max-attempts-per-day" value="10" min="1" max="100">
                        </div>
                        <button id="save-user-settings" class="btn btn-primary">Save User Settings</button>
                    </div>

                    <div class="settings-card">
                        <h3>🔧 System Maintenance</h3>
                        <div class="maintenance-actions">
                            <button id="optimize-database" class="btn btn-secondary">🔧 Optimize Database</button>
                            <button id="recalculate-tscores" class="btn btn-secondary">📊 Recalculate All T-Scores</button>
                            <button id="cleanup-old-data" class="btn btn-outline">🗑️ Cleanup Old Data</button>
                            <button id="export-system-logs" class="btn btn-outline">📋 Export System Logs</button>
                        </div>

                        <div class="system-info">
                            <h4>System Information</h4>
                            <p>Database Size: <span id="db-size">Calculating...</span></p>
                            <p>Last Backup: <span id="last-backup">Never</span></p>
                            <p>System Version: <span id="system-version">1.0.0</span></p>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h3>🎨 Appearance</h3>
                        <div class="form-group">
                            <label for="theme-select">Theme:</label>
                            <select id="theme-select">
                                <option value="default">Default</option>
                                <option value="dark">Dark Mode</option>
                                <option value="high-contrast">High Contrast</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="primary-color">Primary Color:</label>
                            <input type="color" id="primary-color" value="#667eea">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="show-animations" checked>
                                Enable Animations
                            </label>
                        </div>
                        <button id="save-appearance-settings" class="btn btn-primary">Save Appearance</button>
                    </div>

                    <div class="settings-card">
                        <h3>📧 Notifications</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="email-notifications" checked>
                                Email Notifications
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="admin-email">Admin Email:</label>
                            <input type="email" id="admin-email" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="daily-reports" checked>
                                Daily Usage Reports
                            </label>
                        </div>
                        <button id="save-notification-settings" class="btn btn-primary">Save Notifications</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modals -->
        <div id="user-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2 id="user-modal-title">Add User</h2>
                <form id="user-form">
                    <div class="form-group">
                        <label for="user-name">Name:</label>
                        <input type="text" id="user-name" required>
                    </div>
                    <div class="form-group">
                        <label for="user-email">Email:</label>
                        <input type="email" id="user-email">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('user-modal')">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="question-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2 id="question-modal-title">Add Question</h2>
                <form id="question-form">
                    <div class="form-group">
                        <label for="question-text">Question:</label>
                        <textarea id="question-text" required></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="choice-a">Choice A:</label>
                            <input type="text" id="choice-a" required>
                        </div>
                        <div class="form-group">
                            <label for="choice-b">Choice B:</label>
                            <input type="text" id="choice-b" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="choice-c">Choice C:</label>
                            <input type="text" id="choice-c" required>
                        </div>
                        <div class="form-group">
                            <label for="choice-d">Choice D:</label>
                            <input type="text" id="choice-d" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="correct-answer">Correct Answer:</label>
                            <select id="correct-answer" required>
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                                <option value="D">D</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="question-difficulty">Difficulty:</label>
                            <select id="question-difficulty" required>
                                <option value="1">Very Easy</option>
                                <option value="2">Easy</option>
                                <option value="3">Medium</option>
                                <option value="4">Hard</option>
                                <option value="5">Very Hard</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="question-category">Category:</label>
                        <input type="text" id="question-category" required>
                    </div>
                    <div class="form-group">
                        <label for="question-hint">Hint (optional):</label>
                        <textarea id="question-hint"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('question-modal')">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/dataManager.js"></script>
    <script src="js/csvQuizEngine.js"></script>
    <script src="js/api.js"></script>
    <script src="js/adminDashboard.js"></script>
</body>
</html>

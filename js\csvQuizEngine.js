// CSV Quiz Engine - Handles CSV parsing and quiz logic
class CSVQuizEngine {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.csvPath = 'data/questions.csv';
        this.isLoaded = false;
    }

    // Parse CSV content into question objects
    parseCSV(csvContent) {
        const lines = csvContent.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.replace(/"/g, ''));
        const questions = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            if (values.length === headers.length) {
                const question = {};
                headers.forEach((header, index) => {
                    question[header] = values[index];
                });
                
                // Convert choices to object format
                question.choices = {
                    A: question.choiceA,
                    B: question.choiceB,
                    C: question.choiceC,
                    D: question.choiceD
                };
                
                // Convert difficulty to number
                question.difficulty = parseInt(question.difficulty);
                
                questions.push(question);
            }
        }
        
        return questions;
    }

    // Parse a single CSV line, handling quoted values
    parseCSVLine(line) {
        const values = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        values.push(current.trim());
        return values.map(v => v.replace(/^"|"$/g, ''));
    }

    // Load questions from CSV file
    async loadQuestions() {
        try {
            const response = await fetch(this.csvPath);
            if (!response.ok) {
                throw new Error(`Failed to load CSV: ${response.status}`);
            }
            
            const csvContent = await response.text();
            this.questions = this.parseCSV(csvContent);
            this.isLoaded = true;
            
            console.log(`Loaded ${this.questions.length} questions from CSV`);
            return this.questions;
        } catch (error) {
            console.error('Error loading CSV questions:', error);
            // Fallback to mock data if CSV fails to load
            this.questions = this.generateMockQuestions();
            this.isLoaded = true;
            return this.questions;
        }
    }

    // Get questions filtered by difficulty level
    getQuestionsByDifficulty(difficulty, count = 5) {
        const filtered = this.questions.filter(q => q.difficulty == difficulty);
        return this.shuffleArray(filtered).slice(0, count);
    }

    // Get a random selection of questions
    getRandomQuestions(count = 10) {
        return this.shuffleArray([...this.questions]).slice(0, count);
    }

    // Get questions for adaptive quiz (mixed difficulties)
    getAdaptiveQuestions(startDifficulty = 3, totalQuestions = 20) {
        const adaptiveQuestions = [];
        let currentDifficulty = startDifficulty;
        
        // Get questions for each difficulty level
        for (let i = 1; i <= 5; i++) {
            const questionsForLevel = this.questions.filter(q => q.difficulty == i);
            if (questionsForLevel.length > 0) {
                adaptiveQuestions.push(...questionsForLevel);
            }
        }
        
        // Shuffle and return the requested number
        return this.shuffleArray(adaptiveQuestions).slice(0, totalQuestions);
    }

    // Shuffle array using Fisher-Yates algorithm
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    // Get current question
    getCurrentQuestion() {
        if (this.currentQuestionIndex < this.questions.length) {
            return this.questions[this.currentQuestionIndex];
        }
        return null;
    }

    // Submit answer for current question
    submitAnswer(selectedChoice, timeTaken = 0) {
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) return null;

        const isCorrect = selectedChoice === currentQuestion.correctAnswer;
        const answer = {
            questionId: currentQuestion.id,
            question: currentQuestion.question,
            selectedChoice,
            correctAnswer: currentQuestion.correctAnswer,
            isCorrect,
            timeTaken,
            difficulty: currentQuestion.difficulty,
            category: currentQuestion.category,
            timestamp: new Date().toISOString()
        };

        this.userAnswers.push(answer);
        return answer;
    }

    // Move to next question
    nextQuestion() {
        this.currentQuestionIndex++;
        return this.getCurrentQuestion();
    }

    // Get quiz results and statistics
    async getResults(username = 'Anonymous', category = 'general') {
        const totalQuestions = this.userAnswers.length;
        const correctAnswers = this.userAnswers.filter(a => a.isCorrect).length;
        const rawScore = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

        // Calculate T-score using data manager
        let tScoreData;
        if (typeof dataManager !== 'undefined' && dataManager.db) {
            tScoreData = await dataManager.calculateTScore(rawScore, category);
        } else {
            // Fallback calculation
            const populationMean = 47;
            const populationStdDev = 12;
            const tScore = 50 + 10 * ((rawScore - populationMean) / populationStdDev);
            tScoreData = {
                tScore: Math.max(20, Math.min(80, Math.round(tScore * 10) / 10)),
                populationMean,
                populationStdDev,
                sampleSize: 0,
                percentile: Math.round((1 - Math.abs(tScore - 50) / 50) * 100)
            };
        }

        // Category breakdown
        const categoryStats = {};
        this.userAnswers.forEach(answer => {
            if (!categoryStats[answer.category]) {
                categoryStats[answer.category] = { total: 0, correct: 0 };
            }
            categoryStats[answer.category].total++;
            if (answer.isCorrect) {
                categoryStats[answer.category].correct++;
            }
        });

        // Difficulty breakdown
        const difficultyStats = {};
        this.userAnswers.forEach(answer => {
            const diff = answer.difficulty;
            if (!difficultyStats[diff]) {
                difficultyStats[diff] = { total: 0, correct: 0 };
            }
            difficultyStats[diff].total++;
            if (answer.isCorrect) {
                difficultyStats[diff].correct++;
            }
        });

        const results = {
            username,
            category,
            totalQuestions,
            correctAnswers,
            incorrectAnswers: totalQuestions - correctAnswers,
            rawScore: Math.round(rawScore * 10) / 10,
            tScore: tScoreData.tScore,
            percentile: tScoreData.percentile,
            populationMean: tScoreData.populationMean,
            populationStdDev: tScoreData.populationStdDev,
            sampleSize: tScoreData.sampleSize,
            categoryStats,
            difficultyStats,
            answers: this.userAnswers,
            completedAt: new Date().toISOString()
        };

        // Save results to data manager
        if (typeof dataManager !== 'undefined' && dataManager.db) {
            await dataManager.saveResult(results);

            // Update user data
            const userData = {
                username,
                lastQuizDate: new Date().toISOString().split('T')[0],
                bestTScore: tScoreData.tScore,
                lastRawScore: rawScore,
                lastCategory: category
            };
            await dataManager.saveUser(userData);
        }

        return results;
    }

    // Reset quiz state
    reset() {
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
    }

    // Set custom question set
    setQuestions(questions) {
        this.questions = questions;
        this.reset();
    }

    // Generate mock questions if CSV loading fails
    generateMockQuestions() {
        const mockQuestions = [
            {
                id: "1",
                question: "What is the capital of France?",
                choices: { A: "London", B: "Berlin", C: "Paris", D: "Madrid" },
                correctAnswer: "C",
                hint: "France is known for the Eiffel Tower",
                difficulty: 1,
                category: "Geography"
            },
            {
                id: "2",
                question: "Which planet is known as the Red Planet?",
                choices: { A: "Venus", B: "Mars", C: "Jupiter", D: "Saturn" },
                correctAnswer: "B",
                hint: "This planet appears reddish due to iron oxide",
                difficulty: 1,
                category: "Science"
            },
            {
                id: "3",
                question: "What is 2 + 2?",
                choices: { A: "3", B: "4", C: "5", D: "6" },
                correctAnswer: "B",
                hint: "Basic addition",
                difficulty: 1,
                category: "Math"
            }
        ];
        
        console.log('Using mock questions as fallback');
        return mockQuestions;
    }

    // Get statistics about the question database
    getDatabaseStats() {
        if (!this.isLoaded) return null;

        const stats = {
            totalQuestions: this.questions.length,
            byDifficulty: {},
            byCategory: {},
            difficulties: []
        };

        // Count by difficulty
        for (let i = 1; i <= 5; i++) {
            stats.byDifficulty[i] = this.questions.filter(q => q.difficulty == i).length;
        }

        // Count by category
        this.questions.forEach(q => {
            stats.byCategory[q.category] = (stats.byCategory[q.category] || 0) + 1;
        });

        // Get unique difficulties
        stats.difficulties = [...new Set(this.questions.map(q => q.difficulty))].sort();

        return stats;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSVQuizEngine;
}

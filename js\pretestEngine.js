// Pre-test Engine for Fill-in-Blank and Yes/No Questions
class PretestEngine {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.csvPath = 'data/pretest-questions.csv';
        this.isLoaded = false;
        this.passingThreshold = 80; // 80% required to pass
    }

    // Load pre-test questions from CSV
    async loadQuestions() {
        try {
            const response = await fetch(this.csvPath);
            if (!response.ok) {
                throw new Error(`Failed to load pre-test CSV: ${response.status}`);
            }
            
            const csvContent = await response.text();
            this.questions = this.parseCSV(csvContent);
            this.isLoaded = true;
            
            console.log(`Loaded ${this.questions.length} pre-test questions from CSV`);
            return this.questions;
        } catch (error) {
            console.error('Error loading pre-test CSV questions:', error);
            // Fallback to mock data if CSV fails to load
            this.questions = this.generateMockQuestions();
            this.isLoaded = true;
            return this.questions;
        }
    }

    // Parse CSV content into question objects
    parseCSV(csvContent) {
        const lines = csvContent.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.replace(/"/g, ''));
        const questions = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            if (values.length === headers.length) {
                const question = {};
                headers.forEach((header, index) => {
                    question[header] = values[index];
                });
                
                // Convert difficulty to number
                question.difficulty = parseInt(question.difficulty);
                
                // Process question type specific data
                if (question.type === 'yes-no') {
                    question.choices = ['Yes', 'No'];
                } else if (question.type === 'fill-blank') {
                    question.choices = null; // No choices for fill-in-blank
                }
                
                questions.push(question);
            }
        }
        
        return questions;
    }

    // Parse a single CSV line, handling quoted values
    parseCSVLine(line) {
        const values = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        values.push(current.trim());
        return values.map(v => v.replace(/^"|"$/g, ''));
    }

    // Get random selection of pre-test questions
    getRandomQuestions(count = 10) {
        return this.shuffleArray([...this.questions]).slice(0, count);
    }

    // Get questions by type
    getQuestionsByType(type, count = 5) {
        const filtered = this.questions.filter(q => q.type === type);
        return this.shuffleArray(filtered).slice(0, count);
    }

    // Get mixed pre-test questions
    getMixedQuestions(totalCount = 10) {
        const fillBlankCount = Math.ceil(totalCount * 0.6); // 60% fill-in-blank
        const yesNoCount = totalCount - fillBlankCount; // 40% yes/no
        
        const fillBlankQuestions = this.getQuestionsByType('fill-blank', fillBlankCount);
        const yesNoQuestions = this.getQuestionsByType('yes-no', yesNoCount);
        
        return this.shuffleArray([...fillBlankQuestions, ...yesNoQuestions]);
    }

    // Shuffle array using Fisher-Yates algorithm
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    // Get current question
    getCurrentQuestion() {
        if (this.currentQuestionIndex < this.questions.length) {
            return this.questions[this.currentQuestionIndex];
        }
        return null;
    }

    // Submit answer for current question
    submitAnswer(userAnswer, timeTaken = 0) {
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) return null;

        const isCorrect = this.checkAnswer(currentQuestion, userAnswer);
        const answer = {
            questionId: currentQuestion.id,
            question: currentQuestion.question,
            type: currentQuestion.type,
            userAnswer: userAnswer,
            correctAnswer: currentQuestion.correctAnswer,
            isCorrect,
            timeTaken,
            difficulty: currentQuestion.difficulty,
            category: currentQuestion.category,
            timestamp: new Date().toISOString()
        };

        this.userAnswers.push(answer);
        return answer;
    }

    // Check if answer is correct
    checkAnswer(question, userAnswer) {
        const correctAnswer = question.correctAnswer.toLowerCase().trim();
        const userAnswerClean = userAnswer.toLowerCase().trim();
        
        if (question.type === 'yes-no') {
            return userAnswerClean === correctAnswer;
        } else if (question.type === 'fill-blank') {
            // For fill-in-blank, allow some flexibility in answers
            return this.fuzzyMatch(userAnswerClean, correctAnswer);
        }
        
        return false;
    }

    // Fuzzy matching for fill-in-blank answers
    fuzzyMatch(userAnswer, correctAnswer) {
        // Exact match
        if (userAnswer === correctAnswer) return true;
        
        // Remove common articles and prepositions
        const cleanUser = userAnswer.replace(/\b(the|a|an|of|in|on|at|to|for|with|by)\b/g, '').trim();
        const cleanCorrect = correctAnswer.replace(/\b(the|a|an|of|in|on|at|to|for|with|by)\b/g, '').trim();
        
        if (cleanUser === cleanCorrect) return true;
        
        // Check if user answer contains the correct answer or vice versa
        if (cleanUser.includes(cleanCorrect) || cleanCorrect.includes(cleanUser)) {
            return true;
        }
        
        // Check for common variations
        const variations = this.getAnswerVariations(correctAnswer);
        return variations.some(variation => 
            userAnswer.includes(variation) || variation.includes(userAnswer)
        );
    }

    // Get common variations of an answer
    getAnswerVariations(answer) {
        const variations = [answer];
        
        // Add common variations based on the answer
        const variationMap = {
            'paris': ['paris', 'paris france'],
            'pacific': ['pacific', 'pacific ocean'],
            'romeo': ['romeo', 'romeo montague'],
            'armstrong': ['armstrong', 'neil armstrong'],
            'vinci': ['vinci', 'da vinci', 'leonardo da vinci'],
            'einstein': ['einstein', 'albert einstein'],
            'atom': ['atom', 'atoms'],
            'leaves': ['leaves', 'leaf'],
            'yen': ['yen', 'japanese yen'],
            'nile': ['nile', 'nile river'],
            'seismology': ['seismology', 'seismic study']
        };
        
        const lowerAnswer = answer.toLowerCase();
        if (variationMap[lowerAnswer]) {
            variations.push(...variationMap[lowerAnswer]);
        }
        
        return variations;
    }

    // Move to next question
    nextQuestion() {
        this.currentQuestionIndex++;
        return this.getCurrentQuestion();
    }

    // Get pre-test results
    getResults() {
        const totalQuestions = this.userAnswers.length;
        const correctAnswers = this.userAnswers.filter(a => a.isCorrect).length;
        const rawScore = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
        
        // Pre-test uses simple pass/fail with 80% threshold
        const passed = rawScore >= this.passingThreshold;
        
        // Category breakdown
        const categoryStats = {};
        this.userAnswers.forEach(answer => {
            if (!categoryStats[answer.category]) {
                categoryStats[answer.category] = { total: 0, correct: 0 };
            }
            categoryStats[answer.category].total++;
            if (answer.isCorrect) {
                categoryStats[answer.category].correct++;
            }
        });

        // Type breakdown
        const typeStats = {};
        this.userAnswers.forEach(answer => {
            if (!typeStats[answer.type]) {
                typeStats[answer.type] = { total: 0, correct: 0 };
            }
            typeStats[answer.type].total++;
            if (answer.isCorrect) {
                typeStats[answer.type].correct++;
            }
        });

        return {
            totalQuestions,
            correctAnswers,
            incorrectAnswers: totalQuestions - correctAnswers,
            rawScore: Math.round(rawScore * 10) / 10,
            passed,
            passingThreshold: this.passingThreshold,
            categoryStats,
            typeStats,
            answers: this.userAnswers,
            completedAt: new Date().toISOString(),
            testType: 'pretest'
        };
    }

    // Reset pre-test state
    reset() {
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
    }

    // Set custom question set
    setQuestions(questions) {
        this.questions = questions;
        this.reset();
    }

    // Generate mock questions if CSV loading fails
    generateMockQuestions() {
        const mockQuestions = [
            {
                id: "1",
                question: "The capital of France is ______.",
                type: "fill-blank",
                correctAnswer: "Paris",
                hint: "Think of the city with the Eiffel Tower",
                difficulty: 1,
                category: "Geography"
            },
            {
                id: "2",
                question: "Is the Earth round?",
                type: "yes-no",
                correctAnswer: "Yes",
                hint: "Consider the shape of our planet",
                difficulty: 1,
                category: "Science",
                choices: ["Yes", "No"]
            },
            {
                id: "3",
                question: "Water boils at ______ degrees Celsius.",
                type: "fill-blank",
                correctAnswer: "100",
                hint: "Standard boiling point at sea level",
                difficulty: 1,
                category: "Science"
            }
        ];
        
        console.log('Using mock pre-test questions as fallback');
        return mockQuestions;
    }

    // Get statistics about the pre-test database
    getDatabaseStats() {
        if (!this.isLoaded) return null;

        const stats = {
            totalQuestions: this.questions.length,
            byType: {},
            byCategory: {},
            byDifficulty: {}
        };

        // Count by type
        this.questions.forEach(q => {
            stats.byType[q.type] = (stats.byType[q.type] || 0) + 1;
        });

        // Count by category
        this.questions.forEach(q => {
            stats.byCategory[q.category] = (stats.byCategory[q.category] || 0) + 1;
        });

        // Count by difficulty
        this.questions.forEach(q => {
            stats.byDifficulty[q.difficulty] = (stats.byDifficulty[q.difficulty] || 0) + 1;
        });

        return stats;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PretestEngine;
}

// Hint Management System
class HintManager {
    constructor() {
        this.hintMode = false;
        this.hintsUsed = 0;
        this.hintHistory = [];
        this.currentQuestionHint = null;
        this.hintRevealDelay = 500; // Delay before showing hint content
        
        // Initialize from localStorage
        this.loadHintPreferences();
        this.initializeEventListeners();
    }

    // Load hint preferences from localStorage
    loadHintPreferences() {
        const savedHintMode = localStorage.getItem('adaptiveQuiz_hintMode');
        this.hintMode = savedHintMode === 'true';
        
        const savedHintsUsed = localStorage.getItem('adaptiveQuiz_hintsUsed');
        this.hintsUsed = savedHintsUsed ? parseInt(savedHintsUsed) : 0;
        
        console.log(`Hint mode loaded: ${this.hintMode ? 'ON' : 'OFF'}`);
    }

    // Save hint preferences to localStorage
    saveHintPreferences() {
        localStorage.setItem('adaptiveQuiz_hintMode', this.hintMode.toString());
        localStorage.setItem('adaptiveQuiz_hintsUsed', this.hintsUsed.toString());
    }

    // Initialize event listeners
    initializeEventListeners() {
        // Listen for hint mode toggle
        document.addEventListener('DOMContentLoaded', () => {
            const hintToggle = document.getElementById('hint-mode');
            if (hintToggle) {
                hintToggle.checked = this.hintMode;
                hintToggle.addEventListener('change', (e) => {
                    this.setHintMode(e.target.checked);
                });
            }

            // Listen for show hint button
            const showHintBtn = document.getElementById('show-hint');
            if (showHintBtn) {
                showHintBtn.addEventListener('click', () => {
                    this.showHint();
                });
            }
        });
    }

    // Set hint mode on/off
    setHintMode(enabled) {
        this.hintMode = enabled;
        this.saveHintPreferences();
        this.updateHintUI();
        
        console.log(`Hint mode ${enabled ? 'enabled' : 'disabled'}`);
        
        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('hintModeChanged', {
            detail: { enabled: this.hintMode }
        }));
    }

    // Get current hint mode status
    isHintModeEnabled() {
        return this.hintMode;
    }

    // Update hint UI elements
    updateHintUI() {
        const hintStatus = document.getElementById('hint-status');
        const hintContainer = document.getElementById('hint-container');
        const hintToggle = document.getElementById('hint-mode');
        
        if (hintStatus) {
            hintStatus.textContent = this.hintMode ? '💡 Hints ON' : '💡 Hints OFF';
            hintStatus.style.display = this.hintMode ? 'inline' : 'none';
        }
        
        if (hintContainer) {
            hintContainer.style.display = this.hintMode ? 'block' : 'none';
        }
        
        if (hintToggle) {
            hintToggle.checked = this.hintMode;
        }
    }

    // Set hint for current question
    setCurrentQuestionHint(hintText, questionId) {
        this.currentQuestionHint = {
            text: hintText,
            questionId: questionId,
            revealed: false,
            timestamp: Date.now()
        };
        
        this.resetHintDisplay();
        this.updateHintUI();
    }

    // Show hint for current question
    showHint() {
        if (!this.hintMode || !this.currentQuestionHint) {
            return;
        }

        if (this.currentQuestionHint.revealed) {
            return; // Hint already shown
        }

        const showHintBtn = document.getElementById('show-hint');
        const hintText = document.getElementById('hint-text');
        
        if (showHintBtn && hintText) {
            // Disable the button and show loading
            showHintBtn.disabled = true;
            showHintBtn.textContent = '💡 Loading...';
            
            // Add hint usage tracking
            this.hintsUsed++;
            this.saveHintPreferences();
            
            // Record hint usage
            this.hintHistory.push({
                questionId: this.currentQuestionHint.questionId,
                timestamp: Date.now(),
                hintText: this.currentQuestionHint.text
            });
            
            // Show hint with delay for better UX
            setTimeout(() => {
                hintText.textContent = this.currentQuestionHint.text;
                hintText.style.display = 'block';
                
                // Update button
                showHintBtn.textContent = '💡 Hint Shown';
                showHintBtn.classList.add('hint-used');
                
                // Mark as revealed
                this.currentQuestionHint.revealed = true;
                
                // Add animation
                hintText.style.opacity = '0';
                hintText.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    hintText.style.transition = 'all 0.3s ease';
                    hintText.style.opacity = '1';
                    hintText.style.transform = 'translateY(0)';
                }, 50);
                
                console.log('Hint revealed for question:', this.currentQuestionHint.questionId);
                
                // Trigger custom event
                document.dispatchEvent(new CustomEvent('hintRevealed', {
                    detail: {
                        questionId: this.currentQuestionHint.questionId,
                        hintText: this.currentQuestionHint.text,
                        totalHintsUsed: this.hintsUsed
                    }
                }));
                
            }, this.hintRevealDelay);
        }
    }

    // Reset hint display for new question
    resetHintDisplay() {
        const showHintBtn = document.getElementById('show-hint');
        const hintText = document.getElementById('hint-text');
        
        if (showHintBtn) {
            showHintBtn.disabled = false;
            showHintBtn.textContent = '💡 Show Hint';
            showHintBtn.classList.remove('hint-used');
        }
        
        if (hintText) {
            hintText.style.display = 'none';
            hintText.textContent = '';
            hintText.style.transition = '';
            hintText.style.opacity = '';
            hintText.style.transform = '';
        }
    }

    // Get hint usage statistics
    getHintStats() {
        return {
            totalHintsUsed: this.hintsUsed,
            hintsInCurrentSession: this.hintHistory.length,
            hintMode: this.hintMode,
            hintHistory: this.hintHistory.slice(), // Return copy
            averageHintUsagePerQuestion: this.calculateAverageHintUsage()
        };
    }

    // Calculate average hint usage
    calculateAverageHintUsage() {
        const totalQuestions = adaptiveEngine ? adaptiveEngine.totalQuestions : 0;
        if (totalQuestions === 0) return 0;
        return (this.hintHistory.length / totalQuestions) * 100;
    }

    // Check if hint is available for current question
    isHintAvailable() {
        return this.hintMode && 
               this.currentQuestionHint && 
               this.currentQuestionHint.text && 
               this.currentQuestionHint.text.trim() !== '';
    }

    // Check if hint has been revealed for current question
    isHintRevealed() {
        return this.currentQuestionHint && this.currentQuestionHint.revealed;
    }

    // Get hint text for current question (without revealing)
    getCurrentHintText() {
        return this.currentQuestionHint ? this.currentQuestionHint.text : null;
    }

    // Clear hint history (for new quiz session)
    clearHintHistory() {
        this.hintHistory = [];
        console.log('Hint history cleared for new session');
    }

    // Export hint data for quiz results
    exportHintData() {
        return {
            hintMode: this.hintMode,
            totalHintsUsed: this.hintsUsed,
            sessionHintsUsed: this.hintHistory.length,
            hintHistory: this.hintHistory.map(hint => ({
                questionId: hint.questionId,
                timestamp: hint.timestamp,
                // Don't include hint text in export for privacy
            })),
            hintUsageRate: this.calculateAverageHintUsage()
        };
    }

    // Import hint data (for resuming sessions)
    importHintData(data) {
        if (data) {
            this.hintMode = data.hintMode !== undefined ? data.hintMode : this.hintMode;
            this.hintsUsed = data.totalHintsUsed || this.hintsUsed;
            this.hintHistory = data.hintHistory || [];
            this.saveHintPreferences();
            this.updateHintUI();
        }
    }

    // Provide hint effectiveness feedback
    getHintEffectiveness() {
        if (this.hintHistory.length === 0) {
            return null;
        }

        // This would require integration with quiz results
        // For now, return basic statistics
        return {
            hintsUsed: this.hintHistory.length,
            questionsWithHints: this.hintHistory.length,
            hintUsageRate: this.calculateAverageHintUsage(),
            recommendation: this.getHintRecommendation()
        };
    }

    // Get recommendation about hint usage
    getHintRecommendation() {
        const usageRate = this.calculateAverageHintUsage();
        
        if (usageRate === 0) {
            return "You didn't use any hints. Consider using them to learn more effectively!";
        } else if (usageRate < 25) {
            return "Great job! You used hints sparingly and relied on your knowledge.";
        } else if (usageRate < 50) {
            return "Good balance of hint usage. Hints helped you learn without over-reliance.";
        } else if (usageRate < 75) {
            return "You used hints frequently. Try to challenge yourself more without hints.";
        } else {
            return "Heavy hint usage detected. Consider reviewing the material before retaking.";
        }
    }
}

// Create global hint manager instance
const hintManager = new HintManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HintManager;
}

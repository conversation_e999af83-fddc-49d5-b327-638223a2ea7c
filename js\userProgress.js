// User Progress Page Logic
class UserProgress {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.userResults = [];
        this.userStats = null;
        
        if (!this.currentUser) {
            this.redirectToLogin();
            return;
        }
        
        this.initializeEventListeners();
        this.loadUserData();
    }

    initializeEventListeners() {
        document.getElementById('take-quiz-btn').addEventListener('click', () => {
            window.location.href = 'index.html';
        });

        document.getElementById('view-categories-btn').addEventListener('click', () => {
            window.location.href = 'homepage.html';
        });

        document.getElementById('back-home-btn').addEventListener('click', () => {
            window.location.href = 'homepage.html';
        });

        document.getElementById('first-quiz-btn')?.addEventListener('click', () => {
            window.location.href = 'index.html';
        });
    }

    async loadUserData() {
        try {
            // Wait for data manager to initialize
            if (!dataManager.db) {
                setTimeout(() => this.loadUserData(), 100);
                return;
            }

            // Load user data and results
            this.userStats = await dataManager.getUser(this.currentUser);
            this.userResults = await dataManager.getUserResults(this.currentUser);

            if (this.userResults.length === 0) {
                this.showNoDataMessage();
                return;
            }

            this.updateUserHeader();
            this.updateQuickStats();
            this.updateAchievements();
            this.createCharts();
            this.updateCategoryBreakdown();
            this.updateQuizHistory();
            this.generateRecommendations();

        } catch (error) {
            console.error('Error loading user data:', error);
            this.showError('Failed to load user progress data.');
        }
    }

    updateUserHeader() {
        document.getElementById('user-name').textContent = `${this.currentUser}'s Progress`;
        document.getElementById('user-subtitle').textContent = 
            `Member since ${this.userStats?.firstQuizDate ? 
                new Date(this.userStats.firstQuizDate).toLocaleDateString() : 'recently'}`;
        
        // Set user avatar initial
        const avatar = document.getElementById('user-avatar');
        avatar.textContent = this.currentUser.charAt(0).toUpperCase();
    }

    updateQuickStats() {
        const bestTScore = Math.max(...this.userResults.map(r => r.tScore || 0));
        const avgAccuracy = this.userResults.reduce((sum, r) => sum + (r.rawScore || 0), 0) / this.userResults.length;
        
        document.getElementById('best-tscore').textContent = bestTScore.toFixed(1);
        document.getElementById('total-quizzes').textContent = this.userResults.length;
        document.getElementById('avg-accuracy').textContent = `${Math.round(avgAccuracy)}%`;

        // Excellence progress (to T-Score 70)
        const excellenceProgress = Math.min(100, (bestTScore / 70) * 100);
        document.getElementById('excellence-progress').textContent = `${Math.round(excellenceProgress)}%`;
        document.getElementById('excellence-bar').style.width = `${excellenceProgress}%`;

        // Consistency score (based on standard deviation of scores)
        const scores = this.userResults.map(r => r.rawScore || 0);
        const mean = scores.reduce((a, b) => a + b, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const stdDev = Math.sqrt(variance);
        const consistencyScore = Math.max(0, 100 - (stdDev * 2)); // Lower std dev = higher consistency
        
        document.getElementById('consistency-score').textContent = `${Math.round(consistencyScore)}%`;
        document.getElementById('consistency-bar').style.width = `${consistencyScore}%`;
    }

    updateAchievements() {
        const achievements = this.calculateAchievements();
        const container = document.getElementById('achievements-container');
        
        if (achievements.length === 0) {
            container.innerHTML = '<p style="color: #666; text-align: center;">No achievements yet. Keep taking quizzes!</p>';
            return;
        }

        container.innerHTML = achievements.map(achievement => 
            `<div class="achievement-badge badge-${achievement.type}">${achievement.icon} ${achievement.name}</div>`
        ).join('');
    }

    calculateAchievements() {
        const achievements = [];
        const bestTScore = Math.max(...this.userResults.map(r => r.tScore || 0));
        const totalQuizzes = this.userResults.length;
        const perfectScores = this.userResults.filter(r => r.rawScore === 100).length;
        
        // T-Score achievements
        if (bestTScore >= 70) achievements.push({ name: 'Excellence', icon: '🏆', type: 'gold' });
        else if (bestTScore >= 60) achievements.push({ name: 'Proficient', icon: '🥈', type: 'silver' });
        else if (bestTScore >= 50) achievements.push({ name: 'Average', icon: '🥉', type: 'bronze' });

        // Quiz count achievements
        if (totalQuizzes >= 50) achievements.push({ name: 'Quiz Master', icon: '👑', type: 'gold' });
        else if (totalQuizzes >= 20) achievements.push({ name: 'Dedicated', icon: '💪', type: 'silver' });
        else if (totalQuizzes >= 5) achievements.push({ name: 'Getting Started', icon: '🌟', type: 'bronze' });

        // Perfect score achievements
        if (perfectScores >= 5) achievements.push({ name: 'Perfectionist', icon: '💯', type: 'gold' });
        else if (perfectScores >= 1) achievements.push({ name: 'Perfect Score', icon: '✨', type: 'silver' });

        // Streak achievements
        const currentStreak = this.calculateCurrentStreak();
        if (currentStreak >= 10) achievements.push({ name: 'On Fire', icon: '🔥', type: 'gold' });
        else if (currentStreak >= 5) achievements.push({ name: 'Hot Streak', icon: '⚡', type: 'silver' });

        return achievements;
    }

    calculateCurrentStreak() {
        let streak = 0;
        const sortedResults = this.userResults
            .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt));
        
        for (let result of sortedResults) {
            if (result.rawScore >= 70) { // Consider 70%+ as "passing"
                streak++;
            } else {
                break;
            }
        }
        return streak;
    }

    createCharts() {
        this.createTScoreChart();
        this.createCategoryChart();
    }

    createTScoreChart() {
        const ctx = document.getElementById('tscore-chart').getContext('2d');
        
        // Sort results by date
        const sortedResults = this.userResults
            .sort((a, b) => new Date(a.completedAt) - new Date(b.completedAt));
        
        const labels = sortedResults.map((_, index) => `Quiz ${index + 1}`);
        const tScores = sortedResults.map(r => r.tScore || 0);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'T-Score',
                    data: tScores,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }, {
                    label: 'Excellence Threshold',
                    data: new Array(labels.length).fill(70),
                    borderColor: '#4CAF50',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    fill: false,
                    pointRadius: 0
                }, {
                    label: 'Average',
                    data: new Array(labels.length).fill(50),
                    borderColor: '#FF9800',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    fill: false,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 80,
                        ticks: {
                            stepSize: 10
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                const index = context[0].dataIndex;
                                const result = sortedResults[index];
                                return `Quiz ${index + 1} - ${new Date(result.completedAt).toLocaleDateString()}`;
                            },
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return `T-Score: ${context.parsed.y}`;
                                }
                                return context.dataset.label;
                            }
                        }
                    }
                }
            }
        });
    }

    createCategoryChart() {
        const ctx = document.getElementById('category-chart').getContext('2d');

        // Group results by category
        const categoryStats = {};
        this.userResults.forEach(result => {
            const category = result.category || 'General';
            if (!categoryStats[category]) {
                categoryStats[category] = { scores: [], count: 0 };
            }
            categoryStats[category].scores.push(result.rawScore || 0);
            categoryStats[category].count++;
        });

        // Calculate average scores per category
        const categories = Object.keys(categoryStats);
        const averageScores = categories.map(cat => {
            const scores = categoryStats[cat].scores;
            return scores.reduce((a, b) => a + b, 0) / scores.length;
        });

        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: categories,
                datasets: [{
                    label: 'Average Score (%)',
                    data: averageScores,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    borderWidth: 2,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    updateCategoryBreakdown() {
        const container = document.getElementById('category-breakdown');

        // Group results by category
        const categoryStats = {};
        this.userResults.forEach(result => {
            const category = result.category || 'General';
            if (!categoryStats[category]) {
                categoryStats[category] = {
                    scores: [],
                    tScores: [],
                    count: 0,
                    bestScore: 0,
                    bestTScore: 0
                };
            }
            const stats = categoryStats[category];
            stats.scores.push(result.rawScore || 0);
            stats.tScores.push(result.tScore || 0);
            stats.count++;
            stats.bestScore = Math.max(stats.bestScore, result.rawScore || 0);
            stats.bestTScore = Math.max(stats.bestTScore, result.tScore || 0);
        });

        const categoryHTML = Object.entries(categoryStats).map(([category, stats]) => {
            const avgScore = stats.scores.reduce((a, b) => a + b, 0) / stats.scores.length;
            const avgTScore = stats.tScores.reduce((a, b) => a + b, 0) / stats.tScores.length;

            return `
                <div class="category-item">
                    <div class="category-name">${category}</div>
                    <div class="category-stats">
                        <div>Quizzes: ${stats.count}</div>
                        <div>Avg: ${Math.round(avgScore)}% (T: ${avgTScore.toFixed(1)})</div>
                        <div>Best: ${Math.round(stats.bestScore)}% (T: ${stats.bestTScore.toFixed(1)})</div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = categoryHTML || '<p style="color: #666; text-align: center;">No category data available.</p>';
    }

    updateQuizHistory() {
        const container = document.getElementById('quiz-history');

        // Sort results by date (most recent first)
        const sortedResults = this.userResults
            .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))
            .slice(0, 10); // Show last 10 quizzes

        const historyHTML = sortedResults.map(result => {
            const date = new Date(result.completedAt).toLocaleDateString();
            const time = new Date(result.completedAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            return `
                <div class="quiz-item">
                    <div class="quiz-info">
                        <div class="quiz-title">${result.category || 'General Knowledge'} Quiz</div>
                        <div class="quiz-meta">${date} at ${time} • ${result.totalQuestions} questions</div>
                    </div>
                    <div class="quiz-score">
                        <div class="score-value" style="color: ${this.getScoreColor(result.rawScore)}">${Math.round(result.rawScore)}%</div>
                        <div class="score-tscore">T-Score: ${result.tScore?.toFixed(1) || '--'}</div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = historyHTML || '<p style="color: #666; text-align: center;">No quiz history available.</p>';
    }

    generateRecommendations() {
        const container = document.getElementById('recommendations-content');
        const recommendations = this.calculateRecommendations();

        const recommendationsHTML = recommendations.map(rec => `
            <div class="recommendation-item">
                <strong>${rec.title}</strong><br>
                ${rec.description}
            </div>
        `).join('');

        container.innerHTML = recommendationsHTML;
    }

    calculateRecommendations() {
        const recommendations = [];
        const bestTScore = Math.max(...this.userResults.map(r => r.tScore || 0));
        const recentResults = this.userResults
            .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))
            .slice(0, 5);

        // Performance-based recommendations
        if (bestTScore < 50) {
            recommendations.push({
                title: "Focus on Fundamentals",
                description: "Your T-Score suggests focusing on easier questions first. Try categories with lower difficulty levels to build confidence."
            });
        } else if (bestTScore < 60) {
            recommendations.push({
                title: "Steady Progress",
                description: "You're making good progress! Try mixing medium and hard difficulty questions to reach the qualifying range (T-Score 60+)."
            });
        } else if (bestTScore < 70) {
            recommendations.push({
                title: "Push for Excellence",
                description: "You're in the qualifying range! Challenge yourself with harder questions to reach excellence (T-Score 70+)."
            });
        } else {
            recommendations.push({
                title: "Maintain Excellence",
                description: "Excellent performance! Continue challenging yourself and explore new categories to maintain your high standards."
            });
        }

        // Category-based recommendations
        const categoryPerformance = this.analyzeCategoryPerformance();
        if (categoryPerformance.weakest) {
            recommendations.push({
                title: `Improve in ${categoryPerformance.weakest}`,
                description: `Your performance in ${categoryPerformance.weakest} could use some work. Consider taking more quizzes in this category.`
            });
        }

        if (categoryPerformance.strongest) {
            recommendations.push({
                title: `Excel in ${categoryPerformance.strongest}`,
                description: `You're doing great in ${categoryPerformance.strongest}! Consider taking advanced quizzes in this area.`
            });
        }

        // Frequency-based recommendations
        const daysSinceLastQuiz = this.getDaysSinceLastQuiz();
        if (daysSinceLastQuiz > 7) {
            recommendations.push({
                title: "Stay Active",
                description: "It's been a while since your last quiz. Regular practice helps maintain and improve your performance!"
            });
        }

        return recommendations;
    }

    analyzeCategoryPerformance() {
        const categoryStats = {};
        this.userResults.forEach(result => {
            const category = result.category || 'General';
            if (!categoryStats[category]) {
                categoryStats[category] = { scores: [], count: 0 };
            }
            categoryStats[category].scores.push(result.rawScore || 0);
            categoryStats[category].count++;
        });

        let strongest = null;
        let weakest = null;
        let highestAvg = 0;
        let lowestAvg = 100;

        Object.entries(categoryStats).forEach(([category, stats]) => {
            if (stats.count >= 2) { // Only consider categories with at least 2 attempts
                const avg = stats.scores.reduce((a, b) => a + b, 0) / stats.scores.length;
                if (avg > highestAvg) {
                    highestAvg = avg;
                    strongest = category;
                }
                if (avg < lowestAvg) {
                    lowestAvg = avg;
                    weakest = category;
                }
            }
        });

        return { strongest, weakest };
    }

    getDaysSinceLastQuiz() {
        if (this.userResults.length === 0) return 0;

        const lastQuiz = this.userResults
            .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))[0];

        const lastQuizDate = new Date(lastQuiz.completedAt);
        const now = new Date();
        const diffTime = Math.abs(now - lastQuizDate);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    getScoreColor(score) {
        if (score >= 90) return '#4CAF50';
        if (score >= 80) return '#8BC34A';
        if (score >= 70) return '#FFC107';
        if (score >= 60) return '#FF9800';
        return '#F44336';
    }

    showNoDataMessage() {
        document.getElementById('no-data').style.display = 'block';
        document.querySelector('.progress-grid').style.display = 'none';
        document.getElementById('recommendations').style.display = 'none';
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1000;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 5000);
    }

    getCurrentUser() {
        return localStorage.getItem('currentUser') || null;
    }

    redirectToLogin() {
        alert('Please log in to view your progress.');
        window.location.href = 'homepage.html';
    }
}

// Initialize user progress when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.userProgress = new UserProgress();
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Results - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- MathJax for LaTeX support -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
</head>
<body>
    <div class="container">
        <div class="results-container">
            <div class="results-header">
                <h1>🎉 Quiz Complete!</h1>
                <h2 id="user-name">Great job, User!</h2>
            </div>

            <div class="results-grid">
                <!-- Score Summary -->
                <div class="result-card">
                    <h3>📊 Score Summary</h3>
                    <div class="score-details">
                        <div class="score-item">
                            <span class="label">Total Correct:</span>
                            <span id="total-correct" class="value">0</span>
                        </div>
                        <div class="score-item">
                            <span class="label">Total Questions:</span>
                            <span id="total-questions" class="value">0</span>
                        </div>
                        <div class="score-item">
                            <span class="label">Raw Score:</span>
                            <span id="raw-score" class="value">0%</span>
                        </div>
                        <div class="score-item highlight">
                            <span class="label">T-Score:</span>
                            <span id="t-score" class="value t-score-value">50</span>
                        </div>
                    </div>
                </div>

                <!-- Performance by Difficulty -->
                <div class="result-card">
                    <h3>📈 Performance by Difficulty</h3>
                    <div id="difficulty-breakdown" class="difficulty-breakdown">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <!-- T-Score Analysis -->
                <div class="result-card full-width">
                    <h3>🎯 T-Score Analysis</h3>
                    <div class="t-score-analysis">
                        <div class="chart-container">
                            <canvas id="t-score-chart"></canvas>
                        </div>
                        <div class="analysis-text">
                            <div id="performance-message" class="performance-message">
                                <!-- Performance message will be inserted here -->
                            </div>
                            <div class="t-score-explanation">
                                <h4>Understanding Your T-Score:</h4>
                                <ul>
                                    <li><strong>T-Score 50:</strong> Average performance</li>
                                    <li><strong>T-Score 60-70:</strong> Qualifying range (above average)</li>
                                    <li><strong>T-Score 70+:</strong> Excellent performance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Adaptive Journey -->
                <div class="result-card full-width">
                    <h3>🔄 Your Adaptive Journey</h3>
                    <div class="journey-container">
                        <canvas id="difficulty-journey-chart"></canvas>
                    </div>
                    <p class="journey-description">
                        This chart shows how the quiz adapted to your performance,
                        adjusting difficulty based on your answers.
                    </p>
                </div>

                <!-- Performance Analysis -->
                <div class="result-card full-width">
                    <h3>📈 Performance Analysis</h3>
                    <div class="analysis-grid">
                        <div class="chart-section">
                            <h4>Category Performance</h4>
                            <canvas id="category-performance-chart"></canvas>
                        </div>
                        <div class="chart-section">
                            <h4>Response Time Analysis</h4>
                            <canvas id="response-time-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Detailed Statistics -->
                <div class="result-card full-width">
                    <h3>📊 Detailed Statistics</h3>
                    <div class="stats-charts-grid">
                        <div class="chart-section">
                            <h4>Difficulty Distribution</h4>
                            <canvas id="difficulty-distribution-chart"></canvas>
                        </div>
                        <div class="chart-section">
                            <h4>Time vs Accuracy</h4>
                            <canvas id="time-accuracy-chart"></canvas>
                        </div>
                        <div class="chart-section">
                            <h4>Performance Radar</h4>
                            <canvas id="performance-radar-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Comparison Charts -->
                <div class="result-card full-width">
                    <h3>🎯 Performance Comparison</h3>
                    <div class="comparison-grid">
                        <div class="chart-section">
                            <h4>T-Score vs Population</h4>
                            <canvas id="population-comparison-chart"></canvas>
                        </div>
                        <div class="chart-section">
                            <h4>Historical Progress</h4>
                            <canvas id="historical-progress-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="results-actions">
                <button id="retake-quiz" class="btn btn-primary">Take Quiz Again</button>
                <a href="user-progress.html" class="btn btn-secondary">📈 View Progress</a>
                <a href="homepage.html" class="btn btn-secondary">🏠 Home</a>
                <button id="view-leaderboard" class="btn btn-outline">View Leaderboard</button>
                <button id="share-results" class="btn btn-outline">Share Results</button>
            </div>

            <!-- Detailed Stats (Collapsible) -->
            <div class="detailed-stats">
                <button id="toggle-details" class="btn btn-link">Show Detailed Statistics</button>
                <div id="detailed-content" class="detailed-content" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Average Response Time:</span>
                            <span id="avg-response-time" class="stat-value">0s</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Hints Used:</span>
                            <span id="hints-used" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Difficulty Changes:</span>
                            <span id="difficulty-changes" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Best Streak:</span>
                            <span id="best-streak" class="stat-value">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/textProcessor.js"></script>
    <script src="js/api.js"></script>
    <script src="js/results.js"></script>
    <script>
        // Initialize results page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeResults();
        });
    </script>
</body>
</html>

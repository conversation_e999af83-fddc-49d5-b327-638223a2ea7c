<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Adaptive Learning Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .page-link {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .page-link:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <h1>🔧 Debug Test - Adaptive Learning Platform</h1>
    
    <div class="test-section">
        <h2>📋 Quick Page Navigation</h2>
        <p>Test all pages for functionality:</p>
        <a href="homepage.html" class="page-link" target="_blank">🏠 Homepage</a>
        <a href="index.html" class="page-link" target="_blank">📝 Main Quiz</a>
        <a href="pretest.html" class="page-link" target="_blank">📋 Pre-test</a>
        <a href="result.html" class="page-link" target="_blank">📊 Results</a>
        <a href="pretest-result.html" class="page-link" target="_blank">📈 Pre-test Results</a>
        <a href="user-progress.html" class="page-link" target="_blank">👤 User Progress</a>
        <a href="admin.html" class="page-link" target="_blank">⚙️ Admin Dashboard</a>
        <a href="navigation.html" class="page-link" target="_blank">📍 Navigation</a>
    </div>

    <div class="test-section">
        <h2>🧪 JavaScript Functionality Tests</h2>
        <div class="test-grid">
            <div>
                <h3>Core Dependencies</h3>
                <button onclick="testCoreDependencies()">Test Core Scripts</button>
                <div id="core-results"></div>
            </div>
            
            <div>
                <h3>Text Processing</h3>
                <button onclick="testTextProcessor()">Test LaTeX & Chemistry</button>
                <div id="text-results"></div>
            </div>
            
            <div>
                <h3>Data Management</h3>
                <button onclick="testDataManager()">Test Data Storage</button>
                <div id="data-results"></div>
            </div>
            
            <div>
                <h3>Quiz Engine</h3>
                <button onclick="testQuizEngine()">Test Quiz Logic</button>
                <div id="quiz-results"></div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Feature Tests</h2>
        <div class="test-grid">
            <div>
                <h3>Question Quantity Selection</h3>
                <button onclick="testQuestionQuantity()">Test Quantity Options</button>
                <div id="quantity-results"></div>
            </div>
            
            <div>
                <h3>LaTeX Rendering</h3>
                <button onclick="testLatexRendering()">Test Math Expressions</button>
                <div id="latex-results"></div>
            </div>
            
            <div>
                <h3>Chemistry Notation</h3>
                <button onclick="testChemistryNotation()">Test Chemical Formulas</button>
                <div id="chemistry-results"></div>
            </div>
            
            <div>
                <h3>Navigation Links</h3>
                <button onclick="testNavigationLinks()">Test All Links</button>
                <div id="navigation-results"></div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Sample Data Tests</h2>
        <div id="sample-data">
            <h3>LaTeX Examples:</h3>
            <div id="latex-samples"></div>
            
            <h3>Chemistry Examples:</h3>
            <div id="chemistry-samples"></div>
        </div>
    </div>

    <script>
        // Test functions
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testCoreDependencies() {
            clearResults('core-results');
            
            // Test if core scripts are available
            const tests = [
                { name: 'DataManager', check: () => typeof DataManager !== 'undefined' },
                { name: 'CSVQuizEngine', check: () => typeof CSVQuizEngine !== 'undefined' },
                { name: 'TextProcessor', check: () => typeof TextProcessor !== 'undefined' },
                { name: 'Chart.js', check: () => typeof Chart !== 'undefined' },
                { name: 'MathJax', check: () => typeof MathJax !== 'undefined' }
            ];
            
            tests.forEach(test => {
                try {
                    if (test.check()) {
                        addResult('core-results', `✅ ${test.name} loaded successfully`, 'success');
                    } else {
                        addResult('core-results', `❌ ${test.name} not found`, 'error');
                    }
                } catch (error) {
                    addResult('core-results', `❌ ${test.name} error: ${error.message}`, 'error');
                }
            });
        }

        function testTextProcessor() {
            clearResults('text-results');
            
            try {
                if (typeof TextProcessor === 'undefined') {
                    addResult('text-results', '❌ TextProcessor not loaded', 'error');
                    return;
                }
                
                const processor = new TextProcessor();
                
                // Test chemistry processing
                const chemTests = [
                    { input: 'H2O', expected: 'H₂O' },
                    { input: 'Ca(OH)2', expected: 'Ca(OH)₂' },
                    { input: 'Ca2+', expected: 'Ca²⁺' }
                ];
                
                chemTests.forEach(test => {
                    const result = processor.processChemistryNotation(test.input);
                    if (result.includes('₂') || result.includes('²')) {
                        addResult('text-results', `✅ Chemistry: ${test.input} → ${result}`, 'success');
                    } else {
                        addResult('text-results', `⚠️ Chemistry: ${test.input} → ${result}`, 'warning');
                    }
                });
                
                // Test LaTeX processing
                const latexTests = [
                    'x^2 + y^2 = z^2',
                    '\\sqrt{64}',
                    '\\alpha + \\beta'
                ];
                
                latexTests.forEach(test => {
                    const result = processor.processLatex(test);
                    addResult('text-results', `✅ LaTeX: ${test} → ${result}`, 'success');
                });
                
            } catch (error) {
                addResult('text-results', `❌ TextProcessor error: ${error.message}`, 'error');
            }
        }

        function testDataManager() {
            clearResults('data-results');
            
            try {
                if (typeof DataManager === 'undefined') {
                    addResult('data-results', '❌ DataManager not loaded', 'error');
                    return;
                }
                
                const dataManager = new DataManager();
                addResult('data-results', '✅ DataManager initialized', 'success');
                
                // Test localStorage
                const testData = { test: 'value', timestamp: Date.now() };
                localStorage.setItem('debug-test', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('debug-test'));
                
                if (retrieved && retrieved.test === 'value') {
                    addResult('data-results', '✅ localStorage working', 'success');
                } else {
                    addResult('data-results', '❌ localStorage test failed', 'error');
                }
                
                localStorage.removeItem('debug-test');
                
            } catch (error) {
                addResult('data-results', `❌ DataManager error: ${error.message}`, 'error');
            }
        }

        function testQuizEngine() {
            clearResults('quiz-results');
            
            try {
                if (typeof CSVQuizEngine === 'undefined') {
                    addResult('quiz-results', '❌ CSVQuizEngine not loaded', 'error');
                    return;
                }
                
                const engine = new CSVQuizEngine();
                addResult('quiz-results', '✅ CSVQuizEngine initialized', 'success');
                
                // Test question quantity options
                const quantities = [10, 20, 30, 40, 50];
                quantities.forEach(qty => {
                    addResult('quiz-results', `✅ Quantity option: ${qty} questions`, 'success');
                });
                
            } catch (error) {
                addResult('quiz-results', `❌ CSVQuizEngine error: ${error.message}`, 'error');
            }
        }

        function testQuestionQuantity() {
            clearResults('quantity-results');
            
            const quantities = [10, 20, 30, 40, 50];
            quantities.forEach(qty => {
                addResult('quantity-results', `✅ ${qty} questions (${Math.ceil(qty * 0.5)} min estimated)`, 'success');
            });
            
            addResult('quantity-results', '✅ All quantity options available', 'success');
        }

        function testLatexRendering() {
            clearResults('latex-results');
            
            const latexExamples = [
                '$x^2 + y^2 = z^2$',
                '$\\frac{a}{b} = \\frac{c}{d}$',
                '$\\sqrt{x + y}$',
                '$\\alpha + \\beta = \\gamma$',
                '$\\sum_{i=1}^{n} x_i$'
            ];
            
            const container = document.getElementById('latex-samples');
            container.innerHTML = '';
            
            latexExamples.forEach(example => {
                const div = document.createElement('div');
                div.innerHTML = example;
                div.style.margin = '10px 0';
                div.style.padding = '10px';
                div.style.background = '#f8f9fa';
                div.style.borderRadius = '5px';
                container.appendChild(div);
                
                addResult('latex-results', `✅ Added: ${example}`, 'success');
            });
            
            // Trigger MathJax if available
            if (typeof MathJax !== 'undefined') {
                MathJax.typesetPromise([container]).then(() => {
                    addResult('latex-results', '✅ MathJax rendering completed', 'success');
                }).catch(error => {
                    addResult('latex-results', `❌ MathJax error: ${error.message}`, 'error');
                });
            } else {
                addResult('latex-results', '⚠️ MathJax not loaded', 'warning');
            }
        }

        function testChemistryNotation() {
            clearResults('chemistry-results');
            
            const chemExamples = [
                'H2O', 'CO2', 'NaCl', 'CaCO3', 'H2SO4',
                'Ca(OH)2', 'Al2(SO4)3', 'Ca2+', 'Cl-', 'SO4 2-'
            ];
            
            const container = document.getElementById('chemistry-samples');
            container.innerHTML = '';
            
            if (typeof TextProcessor !== 'undefined') {
                const processor = new TextProcessor();
                
                chemExamples.forEach(example => {
                    const processed = processor.processChemistryNotation(example);
                    const div = document.createElement('div');
                    div.innerHTML = `${example} → ${processed}`;
                    div.style.margin = '5px 0';
                    div.style.padding = '8px';
                    div.style.background = '#e8f5e8';
                    div.style.borderRadius = '3px';
                    container.appendChild(div);
                    
                    addResult('chemistry-results', `✅ ${example} → ${processed}`, 'success');
                });
            } else {
                addResult('chemistry-results', '❌ TextProcessor not available', 'error');
            }
        }

        function testNavigationLinks() {
            clearResults('navigation-results');
            
            const pages = [
                'homepage.html', 'index.html', 'pretest.html', 
                'result.html', 'pretest-result.html', 'user-progress.html',
                'admin.html', 'navigation.html'
            ];
            
            pages.forEach(page => {
                fetch(page, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            addResult('navigation-results', `✅ ${page} accessible`, 'success');
                        } else {
                            addResult('navigation-results', `❌ ${page} not found (${response.status})`, 'error');
                        }
                    })
                    .catch(error => {
                        addResult('navigation-results', `❌ ${page} error: ${error.message}`, 'error');
                    });
            });
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testCoreDependencies();
                testQuestionQuantity();
            }, 1000);
        });
    </script>
</body>
</html>

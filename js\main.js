// Main Quiz Application Logic
class QuizApp {
    constructor() {
        this.csvEngine = new CSVQuizEngine();
        this.adaptiveEngine = new AdaptiveEngine();
        this.hintManager = new HintManager();
        
        this.currentUser = '';
        this.hintModeEnabled = false;
        this.quizQuestions = [];
        this.currentQuestionIndex = 0;
        this.startTime = null;
        this.questionStartTime = null;
        
        this.initializeEventListeners();
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Welcome screen events
        document.getElementById('start-quiz').addEventListener('click', () => this.startQuiz());
        document.getElementById('username').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.startQuiz();
        });

        // Quiz screen events
        document.getElementById('submit-answer').addEventListener('click', () => this.submitAnswer());
        document.getElementById('next-question').addEventListener('click', () => this.nextQuestion());
        document.getElementById('show-hint').addEventListener('click', () => this.showHint());

        // Choice selection events
        document.addEventListener('change', (e) => {
            if (e.target.name === 'choice') {
                this.enableSubmitButton();
            }
        });
    }

    // Start the quiz
    async startQuiz() {
        const usernameInput = document.getElementById('username');
        const hintModeCheckbox = document.getElementById('hint-mode');
        
        this.currentUser = usernameInput.value.trim();
        this.hintModeEnabled = hintModeCheckbox.checked;
        
        if (!this.currentUser) {
            alert('Please enter your name to start the quiz.');
            usernameInput.focus();
            return;
        }

        // Show loading screen
        this.showScreen('loading-screen');
        
        try {
            // Load questions from CSV
            await this.csvEngine.loadQuestions();
            
            // Get adaptive questions (20 questions, mixed difficulties)
            this.quizQuestions = this.csvEngine.getAdaptiveQuestions(3, 20);
            this.csvEngine.setQuestions(this.quizQuestions);
            
            // Update UI
            document.getElementById('current-user').textContent = this.currentUser;
            document.getElementById('total-questions').textContent = this.quizQuestions.length;
            document.getElementById('hint-status').style.display = this.hintModeEnabled ? 'inline' : 'none';
            
            // Start the quiz
            this.startTime = new Date();
            this.currentQuestionIndex = 0;
            this.showQuestion();
            this.showScreen('quiz-screen');
            
        } catch (error) {
            console.error('Error starting quiz:', error);
            alert('Error loading quiz questions. Please try again.');
            this.showScreen('welcome-screen');
        }
    }

    // Display current question
    showQuestion() {
        const question = this.quizQuestions[this.currentQuestionIndex];
        if (!question) {
            this.finishQuiz();
            return;
        }

        this.questionStartTime = new Date();
        
        // Update question info
        document.getElementById('question-number').textContent = this.currentQuestionIndex + 1;
        document.getElementById('question-text').textContent = question.question;
        document.getElementById('current-difficulty').textContent = this.getDifficultyName(question.difficulty);
        document.getElementById('current-difficulty').className = `difficulty-badge difficulty-${question.difficulty}`;
        
        // Create choices
        this.createChoices(question.choices);
        
        // Handle hints
        const hintContainer = document.getElementById('hint-container');
        if (this.hintModeEnabled && question.hint) {
            hintContainer.style.display = 'block';
            this.hintManager.setupHint(question.hint);
        } else {
            hintContainer.style.display = 'none';
        }
        
        // Reset UI state
        document.getElementById('submit-answer').disabled = true;
        document.getElementById('next-question').style.display = 'none';
        document.getElementById('hint-text').style.display = 'none';
    }

    // Create choice buttons
    createChoices(choices) {
        const container = document.getElementById('choices-container');
        container.innerHTML = '';
        
        Object.entries(choices).forEach(([key, value]) => {
            const choiceDiv = document.createElement('div');
            choiceDiv.className = 'choice-item';
            choiceDiv.innerHTML = `
                <input type="radio" id="choice-${key}" name="choice" value="${key}">
                <label for="choice-${key}" class="choice-label">
                    <span class="choice-letter">${key}</span>
                    <span class="choice-text">${value}</span>
                </label>
            `;
            container.appendChild(choiceDiv);
        });
    }

    // Enable submit button when choice is selected
    enableSubmitButton() {
        document.getElementById('submit-answer').disabled = false;
    }

    // Submit the current answer
    submitAnswer() {
        const selectedChoice = document.querySelector('input[name="choice"]:checked');
        if (!selectedChoice) return;
        
        const timeTaken = new Date() - this.questionStartTime;
        const answer = this.csvEngine.submitAnswer(selectedChoice.value, timeTaken);
        
        // Show correct answer and feedback
        this.showAnswerFeedback(answer);
        
        // Update score display
        this.updateScoreDisplay();
        
        // Disable choices and show next button
        document.querySelectorAll('input[name="choice"]').forEach(input => input.disabled = true);
        document.getElementById('submit-answer').style.display = 'none';
        document.getElementById('next-question').style.display = 'inline-block';
    }

    // Show answer feedback
    showAnswerFeedback(answer) {
        const choices = document.querySelectorAll('.choice-item');
        choices.forEach(choice => {
            const input = choice.querySelector('input');
            const label = choice.querySelector('label');
            
            if (input.value === answer.correctAnswer) {
                label.classList.add('correct');
            } else if (input.checked && !answer.isCorrect) {
                label.classList.add('incorrect');
            }
        });
    }

    // Update score display
    updateScoreDisplay() {
        const results = this.csvEngine.getResults();
        document.getElementById('correct-count').textContent = results.correctAnswers;
        document.getElementById('incorrect-count').textContent = results.incorrectAnswers;
        document.getElementById('accuracy').textContent = `${results.rawScore}%`;
    }

    // Move to next question
    nextQuestion() {
        this.currentQuestionIndex++;
        this.csvEngine.nextQuestion();
        
        if (this.currentQuestionIndex < this.quizQuestions.length) {
            this.showQuestion();
        } else {
            this.finishQuiz();
        }
    }

    // Show hint for current question
    showHint() {
        this.hintManager.showHint();
    }

    // Finish the quiz and show results
    finishQuiz() {
        const results = this.csvEngine.getResults();
        const totalTime = new Date() - this.startTime;
        
        // Store results in localStorage for the results page
        localStorage.setItem('quizResults', JSON.stringify({
            ...results,
            username: this.currentUser,
            totalTime: Math.round(totalTime / 1000), // in seconds
            hintModeUsed: this.hintModeEnabled
        }));
        
        // Redirect to results page
        window.location.href = 'result.html';
    }

    // Get difficulty name from number
    getDifficultyName(difficulty) {
        const names = {
            1: 'Very Easy',
            2: 'Easy', 
            3: 'Medium',
            4: 'Hard',
            5: 'Very Hard'
        };
        return names[difficulty] || 'Medium';
    }

    // Show specific screen
    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }
}

// Initialize the quiz app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.quizApp = new QuizApp();
    
    // Add some helpful console messages
    console.log('CSV Quiz Application loaded successfully!');
    console.log('Questions will be loaded from: data/questions.csv');
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuizApp;
}

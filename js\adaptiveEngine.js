// Adaptive Learning Engine
class AdaptiveEngine {
    constructor() {
        this.currentDifficulty = 3; // Start at Medium
        this.minDifficulty = 1;
        this.maxDifficulty = 5;
        this.correctStreak = 0;
        this.incorrectStreak = 0;
        this.totalQuestions = 0;
        this.correctAnswers = 0;
        this.difficultyHistory = [];
        this.responseHistory = [];
        this.adaptationRules = {
            // Rules for difficulty adjustment
            streakThresholds: {
                increase: 2,    // Increase difficulty after 2 correct in a row
                decrease: 2     // Decrease difficulty after 2 incorrect in a row
            },
            accuracyThresholds: {
                high: 0.8,      // 80% accuracy - can increase difficulty
                low: 0.5        // 50% accuracy - should decrease difficulty
            },
            adaptationSensitivity: {
                conservative: 0.7,  // Less aggressive adaptation
                moderate: 1.0,      // Standard adaptation
                aggressive: 1.3     // More aggressive adaptation
            }
        };
        this.adaptationMode = 'moderate';
    }

    // Initialize the engine with user preferences
    initialize(options = {}) {
        this.currentDifficulty = options.startingDifficulty || 3;
        this.adaptationMode = options.adaptationMode || 'moderate';
        this.maxQuestions = options.maxQuestions || 20;
        
        // Reset all tracking variables
        this.reset();
        
        console.log(`Adaptive Engine initialized: Starting difficulty ${this.currentDifficulty}, Mode: ${this.adaptationMode}`);
    }

    // Reset the engine state
    reset() {
        this.correctStreak = 0;
        this.incorrectStreak = 0;
        this.totalQuestions = 0;
        this.correctAnswers = 0;
        this.difficultyHistory = [];
        this.responseHistory = [];
    }

    // Get current difficulty level
    getCurrentDifficulty() {
        return this.currentDifficulty;
    }

    // Get difficulty name
    getDifficultyName(level = this.currentDifficulty) {
        const names = {
            1: 'Very Easy',
            2: 'Easy', 
            3: 'Medium',
            4: 'Hard',
            5: 'Very Hard'
        };
        return names[level] || 'Unknown';
    }

    // Process user's answer and adapt difficulty
    processAnswer(questionData, userAnswer, responseTime = 0) {
        const isCorrect = userAnswer === questionData.correctAnswer;
        const currentTime = Date.now();
        
        // Record the response
        const response = {
            questionId: questionData.id,
            difficulty: this.currentDifficulty,
            isCorrect,
            responseTime,
            timestamp: currentTime,
            userAnswer,
            correctAnswer: questionData.correctAnswer
        };
        
        this.responseHistory.push(response);
        this.difficultyHistory.push(this.currentDifficulty);
        this.totalQuestions++;
        
        if (isCorrect) {
            this.correctAnswers++;
            this.correctStreak++;
            this.incorrectStreak = 0;
        } else {
            this.correctStreak = 0;
            this.incorrectStreak++;
        }

        // Calculate new difficulty
        const newDifficulty = this.calculateNewDifficulty(response);
        
        // Log the adaptation decision
        this.logAdaptation(this.currentDifficulty, newDifficulty, response);
        
        // Update difficulty
        this.currentDifficulty = newDifficulty;
        
        return {
            isCorrect,
            newDifficulty,
            difficultyChanged: newDifficulty !== this.difficultyHistory[this.difficultyHistory.length - 1],
            currentAccuracy: this.getCurrentAccuracy(),
            streakInfo: {
                correct: this.correctStreak,
                incorrect: this.incorrectStreak
            }
        };
    }

    // Calculate new difficulty based on performance
    calculateNewDifficulty(response) {
        const currentAccuracy = this.getCurrentAccuracy();
        const sensitivity = this.adaptationRules.adaptationSensitivity[this.adaptationMode];
        let difficultyChange = 0;

        // Primary adaptation based on streaks
        if (this.correctStreak >= this.adaptationRules.streakThresholds.increase) {
            difficultyChange = Math.ceil(1 * sensitivity);
        } else if (this.incorrectStreak >= this.adaptationRules.streakThresholds.decrease) {
            difficultyChange = -Math.ceil(1 * sensitivity);
        }

        // Secondary adaptation based on overall accuracy (after 5+ questions)
        if (this.totalQuestions >= 5) {
            if (currentAccuracy >= this.adaptationRules.accuracyThresholds.high && difficultyChange === 0) {
                difficultyChange = Math.ceil(0.5 * sensitivity);
            } else if (currentAccuracy <= this.adaptationRules.accuracyThresholds.low && difficultyChange === 0) {
                difficultyChange = -Math.ceil(0.5 * sensitivity);
            }
        }

        // Response time consideration (faster responses on correct answers = increase difficulty)
        if (response.isCorrect && response.responseTime > 0 && response.responseTime < 10000) { // Less than 10 seconds
            const avgResponseTime = this.getAverageResponseTime();
            if (response.responseTime < avgResponseTime * 0.7) { // 30% faster than average
                difficultyChange += Math.ceil(0.3 * sensitivity);
            }
        }

        // Apply difficulty change with bounds checking
        let newDifficulty = this.currentDifficulty + difficultyChange;
        newDifficulty = Math.max(this.minDifficulty, Math.min(this.maxDifficulty, newDifficulty));

        return newDifficulty;
    }

    // Get current accuracy percentage
    getCurrentAccuracy() {
        if (this.totalQuestions === 0) return 0;
        return this.correctAnswers / this.totalQuestions;
    }

    // Get average response time
    getAverageResponseTime() {
        const validResponses = this.responseHistory.filter(r => r.responseTime > 0);
        if (validResponses.length === 0) return 0;
        
        const totalTime = validResponses.reduce((sum, r) => sum + r.responseTime, 0);
        return totalTime / validResponses.length;
    }

    // Get performance statistics
    getPerformanceStats() {
        const difficultyBreakdown = {};
        const categoryBreakdown = {};
        
        // Initialize difficulty breakdown
        for (let i = 1; i <= 5; i++) {
            difficultyBreakdown[i] = { total: 0, correct: 0, accuracy: 0 };
        }

        // Process response history
        this.responseHistory.forEach(response => {
            const diff = response.difficulty;
            difficultyBreakdown[diff].total++;
            if (response.isCorrect) {
                difficultyBreakdown[diff].correct++;
            }
        });

        // Calculate accuracy for each difficulty
        Object.keys(difficultyBreakdown).forEach(diff => {
            const data = difficultyBreakdown[diff];
            data.accuracy = data.total > 0 ? (data.correct / data.total) * 100 : 0;
        });

        return {
            totalQuestions: this.totalQuestions,
            correctAnswers: this.correctAnswers,
            overallAccuracy: this.getCurrentAccuracy() * 100,
            difficultyBreakdown,
            averageResponseTime: this.getAverageResponseTime(),
            difficultyProgression: this.difficultyHistory.slice(),
            bestStreak: this.getBestCorrectStreak(),
            currentStreak: this.correctStreak,
            adaptationEvents: this.getAdaptationEvents()
        };
    }

    // Get best correct streak
    getBestCorrectStreak() {
        let bestStreak = 0;
        let currentStreak = 0;
        
        this.responseHistory.forEach(response => {
            if (response.isCorrect) {
                currentStreak++;
                bestStreak = Math.max(bestStreak, currentStreak);
            } else {
                currentStreak = 0;
            }
        });
        
        return bestStreak;
    }

    // Get adaptation events (difficulty changes)
    getAdaptationEvents() {
        const events = [];
        
        for (let i = 1; i < this.difficultyHistory.length; i++) {
            const prevDiff = this.difficultyHistory[i - 1];
            const currDiff = this.difficultyHistory[i];
            
            if (prevDiff !== currDiff) {
                events.push({
                    questionNumber: i + 1,
                    from: prevDiff,
                    to: currDiff,
                    change: currDiff - prevDiff,
                    reason: this.getAdaptationReason(i)
                });
            }
        }
        
        return events;
    }

    // Get reason for adaptation
    getAdaptationReason(questionIndex) {
        if (questionIndex < this.responseHistory.length) {
            const response = this.responseHistory[questionIndex];
            const accuracy = (questionIndex + 1) > 0 ? 
                this.responseHistory.slice(0, questionIndex + 1).filter(r => r.isCorrect).length / (questionIndex + 1) : 0;
            
            if (this.correctStreak >= 2) return 'Correct streak';
            if (this.incorrectStreak >= 2) return 'Incorrect streak';
            if (accuracy >= 0.8) return 'High accuracy';
            if (accuracy <= 0.5) return 'Low accuracy';
            if (response.responseTime < this.getAverageResponseTime() * 0.7) return 'Fast response';
        }
        
        return 'Performance-based';
    }

    // Log adaptation decisions for debugging
    logAdaptation(oldDifficulty, newDifficulty, response) {
        if (oldDifficulty !== newDifficulty) {
            console.log(`Difficulty adapted: ${this.getDifficultyName(oldDifficulty)} → ${this.getDifficultyName(newDifficulty)}`);
            console.log(`Reason: ${response.isCorrect ? 'Correct' : 'Incorrect'} answer, Streak: ${response.isCorrect ? this.correctStreak : this.incorrectStreak}, Accuracy: ${(this.getCurrentAccuracy() * 100).toFixed(1)}%`);
        }
    }

    // Export data for persistence
    exportData() {
        return {
            currentDifficulty: this.currentDifficulty,
            correctStreak: this.correctStreak,
            incorrectStreak: this.incorrectStreak,
            totalQuestions: this.totalQuestions,
            correctAnswers: this.correctAnswers,
            difficultyHistory: this.difficultyHistory.slice(),
            responseHistory: this.responseHistory.slice(),
            adaptationMode: this.adaptationMode
        };
    }

    // Import data from persistence
    importData(data) {
        this.currentDifficulty = data.currentDifficulty || 3;
        this.correctStreak = data.correctStreak || 0;
        this.incorrectStreak = data.incorrectStreak || 0;
        this.totalQuestions = data.totalQuestions || 0;
        this.correctAnswers = data.correctAnswers || 0;
        this.difficultyHistory = data.difficultyHistory || [];
        this.responseHistory = data.responseHistory || [];
        this.adaptationMode = data.adaptationMode || 'moderate';
    }
}

// Create global adaptive engine instance
const adaptiveEngine = new AdaptiveEngine();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdaptiveEngine;
}

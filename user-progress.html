<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Progress - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .progress-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .progress-header {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin: 0 auto 20px;
        }
        
        .progress-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .progress-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .progress-card h3 {
            margin: 0 0 20px 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .stat-highlight {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            text-align: center;
            margin: 20px 0;
        }
        
        .progress-bar-container {
            margin: 15px 0;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 6px;
            transition: width 0.3s ease;
        }
        
        .quiz-history {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .quiz-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }
        
        .quiz-item:hover {
            background: #f8f9fa;
        }
        
        .quiz-item:last-child {
            border-bottom: none;
        }
        
        .quiz-info {
            flex: 1;
        }
        
        .quiz-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .quiz-meta {
            font-size: 12px;
            color: #666;
        }
        
        .quiz-score {
            text-align: right;
        }
        
        .score-value {
            font-weight: bold;
            font-size: 18px;
        }
        
        .score-tscore {
            font-size: 12px;
            color: #666;
        }
        
        .achievement-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 2px;
        }
        
        .badge-gold {
            background: #FFD700;
            color: #B8860B;
        }
        
        .badge-silver {
            background: #C0C0C0;
            color: #696969;
        }
        
        .badge-bronze {
            background: #CD7F32;
            color: #8B4513;
        }
        
        .category-progress {
            margin: 15px 0;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-name {
            font-weight: 500;
            color: #333;
        }
        
        .category-stats {
            text-align: right;
            font-size: 14px;
            color: #666;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .no-data {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .no-data-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .action-section {
            text-align: center;
            margin-top: 30px;
        }
        
        .recommendations {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .recommendations h3 {
            margin: 0 0 15px 0;
        }
        
        .recommendation-item {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="progress-container">
        <!-- Header -->
        <div class="progress-header">
            <div class="user-avatar" id="user-avatar">👤</div>
            <h1 id="user-name">My Learning Progress</h1>
            <p id="user-subtitle">Track your quiz performance and achievements</p>
            <div class="action-buttons">
                <button id="take-quiz-btn" class="btn btn-primary">Take New Quiz</button>
                <button id="view-categories-btn" class="btn btn-secondary">Browse Categories</button>
                <button id="back-home-btn" class="btn btn-outline">Back to Home</button>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="progress-grid">
            <div class="progress-card">
                <h3>📊 Overall Performance</h3>
                <div class="stat-highlight" id="best-tscore">--</div>
                <p style="text-align: center; color: #666; margin: 0;">Best T-Score</p>
                
                <div class="progress-bar-container">
                    <div class="progress-label">
                        <span>Progress to Excellence (T-Score 70+)</span>
                        <span id="excellence-progress">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="excellence-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="progress-card">
                <h3>🎯 Quiz Statistics</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center;">
                    <div>
                        <div class="stat-highlight" id="total-quizzes">0</div>
                        <p style="color: #666; margin: 0;">Total Quizzes</p>
                    </div>
                    <div>
                        <div class="stat-highlight" id="avg-accuracy">0%</div>
                        <p style="color: #666; margin: 0;">Avg Accuracy</p>
                    </div>
                </div>
                
                <div class="progress-bar-container">
                    <div class="progress-label">
                        <span>Consistency Score</span>
                        <span id="consistency-score">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="consistency-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="progress-card">
                <h3>🏆 Achievements</h3>
                <div id="achievements-container">
                    <!-- Achievements will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="progress-grid">
            <div class="progress-card">
                <h3>📈 T-Score Progress</h3>
                <div class="chart-container">
                    <canvas id="tscore-chart"></canvas>
                </div>
            </div>

            <div class="progress-card">
                <h3>📚 Category Performance</h3>
                <div class="chart-container">
                    <canvas id="category-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Category Breakdown -->
        <div class="progress-card">
            <h3>📋 Category Breakdown</h3>
            <div id="category-breakdown">
                <!-- Category progress will be loaded here -->
            </div>
        </div>

        <!-- Quiz History -->
        <div class="progress-card">
            <h3>📝 Recent Quiz History</h3>
            <div id="quiz-history" class="quiz-history">
                <!-- Quiz history will be loaded here -->
            </div>
        </div>

        <!-- Recommendations -->
        <div id="recommendations" class="recommendations">
            <h3>💡 Personalized Recommendations</h3>
            <div id="recommendations-content">
                <!-- Recommendations will be loaded here -->
            </div>
        </div>

        <!-- No Data Message -->
        <div id="no-data" class="no-data" style="display: none;">
            <div class="no-data-icon">📊</div>
            <h3>No Quiz Data Yet</h3>
            <p>Take your first quiz to start tracking your progress!</p>
            <button id="first-quiz-btn" class="btn btn-primary">Take Your First Quiz</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/dataManager.js"></script>
    <script src="js/userProgress.js"></script>
</body>
</html>

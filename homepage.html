<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Categories - Adaptive Learning Platform</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .homepage-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .homepage-header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .category-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .category-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .category-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .category-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .category-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
            color: #888;
        }
        
        .category-difficulty {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .difficulty-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e0e0e0;
        }
        
        .difficulty-dot.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .quick-stats {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .user-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .user-welcome {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .user-info h3 {
            margin: 0;
            color: #333;
        }
        
        .user-stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .search-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #667eea;
        }
        
        .filter-select {
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            background: white;
            outline: none;
            cursor: pointer;
        }
        
        .no-categories {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-categories-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* Test Selection Styles */
        .test-selection-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .test-selection-section h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 24px;
        }

        .test-selection-section p {
            margin: 0 0 25px 0;
            color: #666;
            font-size: 16px;
        }

        .test-files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .test-file-option {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .test-file-option:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .test-file-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .test-file-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }

        .test-file-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .test-file-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .test-file-stats {
            display: flex;
            justify-content: space-around;
            font-size: 12px;
            color: #888;
        }

        .test-file-stats span {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .test-file-stats .stat-value {
            font-weight: 600;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="homepage-container">
        <!-- Header -->
        <div class="homepage-header">
            <h1>🎯 Adaptive Learning Platform</h1>
            <p>Choose from our comprehensive collection of quiz categories and start your learning journey!</p>
        </div>

        <!-- User Section -->
        <div id="user-section" class="user-section" style="display: none;">
            <div class="user-welcome">
                <div class="user-info">
                    <h3 id="welcome-message">Welcome back!</h3>
                    <div class="user-stats">
                        <span>Best T-Score: <strong id="user-best-score">--</strong></span>
                        <span>Total Quizzes: <strong id="user-total-quizzes">--</strong></span>
                        <span>Last Quiz: <strong id="user-last-quiz">--</strong></span>
                    </div>
                </div>
                <div class="action-buttons">
                    <button id="view-progress-btn" class="btn btn-secondary">View Progress</button>
                    <button id="logout-btn" class="btn btn-outline">Logout</button>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <h3>📊 Platform Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span id="total-categories" class="stat-value">0</span>
                    <span class="stat-label">Categories</span>
                </div>
                <div class="stat-item">
                    <span id="total-questions" class="stat-value">0</span>
                    <span class="stat-label">Questions</span>
                </div>
                <div class="stat-item">
                    <span id="total-users" class="stat-value">0</span>
                    <span class="stat-label">Users</span>
                </div>
                <div class="stat-item">
                    <span id="avg-score" class="stat-value">0%</span>
                    <span class="stat-label">Avg Score</span>
                </div>
            </div>
        </div>

        <!-- Test Selection -->
        <div class="test-selection-section">
            <h3>📁 Available Test Files</h3>
            <p>Choose from our specialized test collections</p>

            <div class="test-files-grid" id="available-tests">
                <!-- Test files will be populated here -->
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="search-section">
            <h3>🔍 Find Your Quiz</h3>
            <div class="search-bar">
                <input type="text" id="search-input" class="search-input" placeholder="Search categories...">
                <select id="difficulty-filter" class="filter-select">
                    <option value="">All Difficulties</option>
                    <option value="1">Very Easy</option>
                    <option value="2">Easy</option>
                    <option value="3">Medium</option>
                    <option value="4">Hard</option>
                    <option value="5">Very Hard</option>
                </select>
                <select id="question-count-filter" class="filter-select">
                    <option value="10">10 Questions</option>
                    <option value="20" selected>20 Questions</option>
                    <option value="30">30 Questions</option>
                    <option value="40">40 Questions</option>
                    <option value="50">50 Questions</option>
                </select>
                <button id="random-quiz-btn" class="btn btn-primary">Random Quiz</button>
            </div>
        </div>

        <!-- Categories Grid -->
        <div id="categories-container">
            <h3>📚 Quiz Categories</h3>
            <div id="categories-grid" class="categories-grid">
                <!-- Categories will be loaded here -->
            </div>
            
            <div id="no-categories" class="no-categories" style="display: none;">
                <div class="no-categories-icon">📚</div>
                <h3>No Categories Found</h3>
                <p>No quiz categories match your search criteria.</p>
                <button id="clear-search-btn" class="btn btn-secondary">Clear Search</button>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons" style="justify-content: center; margin-top: 40px;">
            <a href="pretest.html" class="btn btn-primary">📋 Take Pre-test</a>
            <button id="create-quiz-btn" class="btn btn-secondary">Create Custom Quiz</button>
            <a href="index.html" class="btn btn-outline">Quick Start Quiz</a>
            <a href="navigation.html" class="btn btn-outline">📍 Site Map</a>
        </div>

        <!-- Admin Access (Discrete) -->
        <div style="text-align: center; margin-top: 20px;">
            <a href="admin.html" style="color: #999; font-size: 12px; text-decoration: none;">⚙️ Admin Access</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/dataManager.js"></script>
    <script src="js/testFileManager.js"></script>
    <script src="js/csvQuizEngine.js"></script>
    <script src="js/homepage.js"></script>
</body>
</html>

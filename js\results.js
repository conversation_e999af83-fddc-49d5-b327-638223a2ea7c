// Results Page Logic for CSV Quiz
function initializeResults() {
    // Get results from localStorage
    const resultsData = localStorage.getItem('quizResults');
    
    if (!resultsData) {
        // No results found, redirect to home
        alert('No quiz results found. Please take a quiz first.');
        window.location.href = 'index.html';
        return;
    }
    
    const results = JSON.parse(resultsData);
    displayResults(results);
    setupEventListeners();
}

function displayResults(results) {
    // Update user name
    document.getElementById('user-name').textContent = `Great job, ${results.username}!`;
    
    // Update score summary
    document.getElementById('total-correct').textContent = results.correctAnswers;
    document.getElementById('total-questions').textContent = results.totalQuestions;
    document.getElementById('raw-score').textContent = `${results.rawScore}%`;
    document.getElementById('t-score').textContent = results.tScore;
    
    // Color code T-score
    const tScoreElement = document.getElementById('t-score');
    if (results.tScore >= 70) {
        tScoreElement.className = 'value t-score-value excellent';
    } else if (results.tScore >= 60) {
        tScoreElement.className = 'value t-score-value good';
    } else if (results.tScore >= 50) {
        tScoreElement.className = 'value t-score-value average';
    } else {
        tScoreElement.className = 'value t-score-value below-average';
    }
    
    // Display difficulty breakdown
    displayDifficultyBreakdown(results.difficultyStats);
    
    // Create charts
    createTScoreChart(results);
    createDifficultyJourneyChart(results);
    createCategoryPerformanceChart(results);
    createResponseTimeChart(results);
    createDifficultyDistributionChart(results);
    createTimeAccuracyChart(results);
    createPerformanceRadarChart(results);
    createPopulationComparisonChart(results);
    createHistoricalProgressChart(results);
    
    // Display performance message
    displayPerformanceMessage(results);
    
    // Display detailed stats
    displayDetailedStats(results);
}

function displayDifficultyBreakdown(difficultyStats) {
    const container = document.getElementById('difficulty-breakdown');
    container.innerHTML = '';
    
    const difficultyNames = {
        1: 'Very Easy',
        2: 'Easy',
        3: 'Medium',
        4: 'Hard',
        5: 'Very Hard'
    };
    
    Object.entries(difficultyStats).forEach(([level, stats]) => {
        const percentage = stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0;
        
        const difficultyItem = document.createElement('div');
        difficultyItem.className = 'difficulty-item';
        difficultyItem.innerHTML = `
            <div class="difficulty-header">
                <span class="difficulty-name">${difficultyNames[level] || `Level ${level}`}</span>
                <span class="difficulty-score">${stats.correct}/${stats.total} (${percentage}%)</span>
            </div>
            <div class="difficulty-bar">
                <div class="difficulty-progress" style="width: ${percentage}%"></div>
            </div>
        `;
        
        container.appendChild(difficultyItem);
    });
}

function createTScoreChart(results) {
    const ctx = document.getElementById('t-score-chart').getContext('2d');

    // Create a comprehensive T-score visualization
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Your T-Score', 'To Excellence (70)', 'To Perfect (80)'],
            datasets: [{
                data: [
                    results.tScore,
                    Math.max(0, 70 - results.tScore),
                    Math.max(0, 80 - Math.max(results.tScore, 70))
                ],
                backgroundColor: [
                    results.tScore >= 70 ? '#4CAF50' :
                    results.tScore >= 60 ? '#FF9800' :
                    results.tScore >= 50 ? '#2196F3' : '#F44336',
                    '#FFE0B2',
                    '#E8F5E8'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        generateLabels: function(chart) {
                            return [
                                {
                                    text: `Your T-Score: ${results.tScore}`,
                                    fillStyle: chart.data.datasets[0].backgroundColor[0],
                                    strokeStyle: '#fff',
                                    lineWidth: 2
                                },
                                {
                                    text: `Population Mean: ${results.populationMean}`,
                                    fillStyle: '#9E9E9E',
                                    strokeStyle: '#fff',
                                    lineWidth: 2
                                },
                                {
                                    text: `Percentile: ${results.percentile}th`,
                                    fillStyle: '#607D8B',
                                    strokeStyle: '#fff',
                                    lineWidth: 2
                                }
                            ];
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const labels = ['Your T-Score', 'Gap to Excellence', 'Gap to Perfect'];
                            return `${labels[context.dataIndex]}: ${context.parsed}`;
                        }
                    }
                }
            }
        },
        plugins: [{
            beforeDraw: function(chart) {
                const width = chart.width;
                const height = chart.height;
                const ctx = chart.ctx;

                ctx.restore();
                ctx.font = "bold 24px sans-serif";
                ctx.textBaseline = "middle";
                ctx.fillStyle = "#333";
                ctx.textAlign = "center";

                // Main T-score
                ctx.fillText(results.tScore.toString(), width / 2, height / 2 - 10);

                // Label
                ctx.font = "12px sans-serif";
                ctx.fillStyle = "#666";
                ctx.fillText("T-Score", width / 2, height / 2 + 15);

                ctx.save();
            }
        }]
    });
}

function createDifficultyJourneyChart(results) {
    const ctx = document.getElementById('difficulty-journey-chart').getContext('2d');

    // Extract difficulty progression from answers
    const difficultyProgression = results.answers.map((answer, index) => ({
        question: index + 1,
        difficulty: answer.difficulty,
        correct: answer.isCorrect,
        timeTaken: answer.timeTaken || 0,
        category: answer.category
    }));

    // Calculate running accuracy
    let runningCorrect = 0;
    const runningAccuracy = difficultyProgression.map((item, index) => {
        if (item.correct) runningCorrect++;
        return (runningCorrect / (index + 1)) * 100;
    });

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: difficultyProgression.map(d => `Q${d.question}`),
            datasets: [{
                label: 'Question Difficulty',
                data: difficultyProgression.map(d => d.difficulty),
                borderColor: '#2196F3',
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: 'Correct Answers',
                data: difficultyProgression.map(d => d.correct ? d.difficulty : null),
                borderColor: '#4CAF50',
                backgroundColor: '#4CAF50',
                borderWidth: 0,
                pointRadius: 8,
                pointHoverRadius: 10,
                showLine: false,
                yAxisID: 'y'
            }, {
                label: 'Incorrect Answers',
                data: difficultyProgression.map(d => !d.correct ? d.difficulty : null),
                borderColor: '#F44336',
                backgroundColor: '#F44336',
                borderWidth: 0,
                pointRadius: 6,
                pointHoverRadius: 8,
                showLine: false,
                yAxisID: 'y'
            }, {
                label: 'Running Accuracy (%)',
                data: runningAccuracy,
                borderColor: '#FF9800',
                backgroundColor: 'rgba(255, 152, 0, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.3,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    max: 5,
                    title: {
                        display: true,
                        text: 'Difficulty Level'
                    },
                    ticks: {
                        stepSize: 1,
                        callback: function(value) {
                            const names = ['', 'Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
                            return names[value] || value;
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    min: 0,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Accuracy (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const index = context[0].dataIndex;
                            const answer = difficultyProgression[index];
                            return `Question ${index + 1} - ${answer.category}`;
                        },
                        label: function(context) {
                            const index = context.dataIndex;
                            const answer = difficultyProgression[index];

                            if (context.datasetIndex === 0) {
                                const names = ['', 'Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
                                return `Difficulty: ${names[context.parsed.y]}`;
                            } else if (context.datasetIndex === 1) {
                                return `✓ Correct (${(answer.timeTaken / 1000).toFixed(1)}s)`;
                            } else if (context.datasetIndex === 2) {
                                return `✗ Incorrect (${(answer.timeTaken / 1000).toFixed(1)}s)`;
                            } else if (context.datasetIndex === 3) {
                                return `Running Accuracy: ${context.parsed.y.toFixed(1)}%`;
                            }
                        }
                    }
                }
            }
        }
    });
}

function createCategoryPerformanceChart(results) {
    const ctx = document.getElementById('category-performance-chart').getContext('2d');

    // Group results by category
    const categoryData = {};
    results.answers.forEach(answer => {
        const category = answer.category || 'General';
        if (!categoryData[category]) {
            categoryData[category] = { correct: 0, total: 0 };
        }
        categoryData[category].total++;
        if (answer.isCorrect) categoryData[category].correct++;
    });

    const categories = Object.keys(categoryData);
    const accuracies = categories.map(cat =>
        (categoryData[cat].correct / categoryData[cat].total) * 100
    );
    const totals = categories.map(cat => categoryData[cat].total);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: categories,
            datasets: [{
                label: 'Accuracy (%)',
                data: accuracies,
                backgroundColor: categories.map((_, i) =>
                    `hsla(${(i * 360 / categories.length)}, 70%, 60%, 0.8)`
                ),
                borderColor: categories.map((_, i) =>
                    `hsla(${(i * 360 / categories.length)}, 70%, 50%, 1)`
                ),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Accuracy (%)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const category = categories[context.dataIndex];
                            const data = categoryData[category];
                            return [
                                `Accuracy: ${context.parsed.y.toFixed(1)}%`,
                                `Correct: ${data.correct}/${data.total}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function createResponseTimeChart(results) {
    const ctx = document.getElementById('response-time-chart').getContext('2d');

    const timeData = results.answers.map((answer, index) => ({
        question: index + 1,
        time: (answer.timeTaken || 0) / 1000,
        correct: answer.isCorrect,
        difficulty: answer.difficulty
    }));

    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: 'Correct Answers',
                data: timeData.filter(d => d.correct).map(d => ({
                    x: d.question,
                    y: d.time
                })),
                backgroundColor: '#4CAF50',
                borderColor: '#4CAF50',
                pointRadius: 6
            }, {
                label: 'Incorrect Answers',
                data: timeData.filter(d => !d.correct).map(d => ({
                    x: d.question,
                    y: d.time
                })),
                backgroundColor: '#F44336',
                borderColor: '#F44336',
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Question Number'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Response Time (seconds)'
                    },
                    beginAtZero: true
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const point = timeData[context.dataIndex];
                            return [
                                `Question ${point.question}`,
                                `Time: ${point.time.toFixed(1)}s`,
                                `Difficulty: ${point.difficulty}`,
                                `Result: ${point.correct ? 'Correct' : 'Incorrect'}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function createDifficultyDistributionChart(results) {
    const ctx = document.getElementById('difficulty-distribution-chart').getContext('2d');

    const difficultyStats = {};
    for (let i = 1; i <= 5; i++) {
        difficultyStats[i] = { correct: 0, total: 0 };
    }

    results.answers.forEach(answer => {
        const diff = answer.difficulty;
        difficultyStats[diff].total++;
        if (answer.isCorrect) difficultyStats[diff].correct++;
    });

    const difficulties = Object.keys(difficultyStats);
    const correctData = difficulties.map(d => difficultyStats[d].correct);
    const incorrectData = difficulties.map(d => difficultyStats[d].total - difficultyStats[d].correct);
    const difficultyNames = ['Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: difficultyNames,
            datasets: [{
                label: 'Correct',
                data: correctData,
                backgroundColor: '#4CAF50',
                borderColor: '#4CAF50',
                borderWidth: 1
            }, {
                label: 'Incorrect',
                data: incorrectData,
                backgroundColor: '#F44336',
                borderColor: '#F44336',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Difficulty Level'
                    }
                },
                y: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Number of Questions'
                    },
                    beginAtZero: true
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const difficulty = difficulties[context.dataIndex];
                            const stats = difficultyStats[difficulty];
                            const accuracy = stats.total > 0 ? (stats.correct / stats.total * 100).toFixed(1) : 0;
                            return `${context.dataset.label}: ${context.parsed.y} (${accuracy}% accuracy)`;
                        }
                    }
                }
            }
        }
    });
}

function createTimeAccuracyChart(results) {
    const ctx = document.getElementById('time-accuracy-chart').getContext('2d');

    // Calculate average time per difficulty and accuracy
    const difficultyData = {};
    for (let i = 1; i <= 5; i++) {
        difficultyData[i] = { times: [], correct: 0, total: 0 };
    }

    results.answers.forEach(answer => {
        const diff = answer.difficulty;
        difficultyData[diff].times.push((answer.timeTaken || 0) / 1000);
        difficultyData[diff].total++;
        if (answer.isCorrect) difficultyData[diff].correct++;
    });

    const scatterData = Object.keys(difficultyData).map(diff => {
        const data = difficultyData[diff];
        const avgTime = data.times.length > 0 ?
            data.times.reduce((a, b) => a + b, 0) / data.times.length : 0;
        const accuracy = data.total > 0 ? (data.correct / data.total) * 100 : 0;

        return {
            x: avgTime,
            y: accuracy,
            difficulty: parseInt(diff),
            total: data.total
        };
    }).filter(d => d.total > 0);

    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: 'Difficulty Levels',
                data: scatterData,
                backgroundColor: scatterData.map(d => {
                    const colors = ['#4CAF50', '#8BC34A', '#FFC107', '#FF9800', '#F44336'];
                    return colors[d.difficulty - 1];
                }),
                borderColor: scatterData.map(d => {
                    const colors = ['#388E3C', '#689F38', '#F57F17', '#F57C00', '#D32F2F'];
                    return colors[d.difficulty - 1];
                }),
                pointRadius: scatterData.map(d => Math.max(8, d.total * 2)),
                pointHoverRadius: scatterData.map(d => Math.max(10, d.total * 2 + 2))
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Average Response Time (seconds)'
                    },
                    beginAtZero: true
                },
                y: {
                    title: {
                        display: true,
                        text: 'Accuracy (%)'
                    },
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const point = scatterData[context.dataIndex];
                            const diffNames = ['Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'];
                            return [
                                `Difficulty: ${diffNames[point.difficulty - 1]}`,
                                `Avg Time: ${point.x.toFixed(1)}s`,
                                `Accuracy: ${point.y.toFixed(1)}%`,
                                `Questions: ${point.total}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function createPerformanceRadarChart(results) {
    const ctx = document.getElementById('performance-radar-chart').getContext('2d');

    // Calculate performance metrics
    const metrics = {
        'Speed': Math.max(0, 100 - (results.answers.reduce((sum, a) => sum + (a.timeTaken || 0), 0) / results.answers.length / 1000) * 10),
        'Accuracy': results.rawScore,
        'Consistency': calculateConsistency(results.answers),
        'Difficulty Handling': calculateDifficultyHandling(results.answers),
        'Category Breadth': calculateCategoryBreadth(results.answers),
        'T-Score Performance': Math.min(100, (results.tScore / 80) * 100)
    };

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: Object.keys(metrics),
            datasets: [{
                label: 'Your Performance',
                data: Object.values(metrics),
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.2)',
                borderWidth: 3,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }, {
                label: 'Average Performance',
                data: [70, 65, 60, 55, 50, 62], // Mock average data
                borderColor: '#FF9800',
                backgroundColor: 'rgba(255, 152, 0, 0.1)',
                borderWidth: 2,
                borderDash: [5, 5],
                pointBackgroundColor: '#FF9800',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
}

function createPopulationComparisonChart(results) {
    const ctx = document.getElementById('population-comparison-chart').getContext('2d');

    // Create T-score distribution comparison
    const tScoreRanges = [
        { range: '20-30', count: 5, color: '#F44336' },
        { range: '30-40', count: 15, color: '#FF9800' },
        { range: '40-50', count: 30, color: '#FFC107' },
        { range: '50-60', count: 25, color: '#4CAF50' },
        { range: '60-70', count: 20, color: '#2196F3' },
        { range: '70-80', count: 5, color: '#9C27B0' }
    ];

    // Find user's range
    const userRange = tScoreRanges.find(r => {
        const [min, max] = r.range.split('-').map(Number);
        return results.tScore >= min && results.tScore < max;
    });

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: tScoreRanges.map(r => r.range),
            datasets: [{
                label: 'Population Distribution',
                data: tScoreRanges.map(r => r.count),
                backgroundColor: tScoreRanges.map(r =>
                    r === userRange ? r.color : r.color + '80'
                ),
                borderColor: tScoreRanges.map(r => r.color),
                borderWidth: tScoreRanges.map(r => r === userRange ? 3 : 1)
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'T-Score Range'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Number of Users'
                    },
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const range = tScoreRanges[context.dataIndex];
                            if (range === userRange) {
                                return `← Your T-Score: ${results.tScore}`;
                            }
                            return '';
                        }
                    }
                }
            }
        }
    });
}

function createHistoricalProgressChart(results) {
    const ctx = document.getElementById('historical-progress-chart').getContext('2d');

    // Get historical data from localStorage or create mock data
    const historicalData = getHistoricalData(results.username);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: historicalData.map((_, index) => `Attempt ${index + 1}`),
            datasets: [{
                label: 'T-Score Progress',
                data: historicalData.map(d => d.tScore),
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }, {
                label: 'Raw Score Progress',
                data: historicalData.map(d => d.rawScore),
                borderColor: '#4CAF50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4,
                pointBackgroundColor: '#4CAF50',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'Score'
                    },
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const data = historicalData[context.dataIndex];
                            return `Date: ${new Date(data.date).toLocaleDateString()}`;
                        }
                    }
                }
            }
        }
    });
}

// Helper functions
function calculateConsistency(answers) {
    const scores = answers.map(a => a.isCorrect ? 1 : 0);
    const mean = scores.reduce((a, b) => a + b, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 100 - (stdDev * 100));
}

function calculateDifficultyHandling(answers) {
    const hardQuestions = answers.filter(a => a.difficulty >= 4);
    if (hardQuestions.length === 0) return 50;
    const hardAccuracy = hardQuestions.filter(a => a.isCorrect).length / hardQuestions.length;
    return hardAccuracy * 100;
}

function calculateCategoryBreadth(answers) {
    const categories = new Set(answers.map(a => a.category));
    return Math.min(100, (categories.size / 5) * 100); // Assuming max 5 categories
}

function getHistoricalData(username) {
    // In a real implementation, this would fetch from the database
    // For now, create mock historical data including current result
    const currentResult = JSON.parse(localStorage.getItem('quizResults'));
    const mockHistory = [
        { tScore: 45, rawScore: 60, date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        { tScore: 48, rawScore: 65, date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) },
        { tScore: 52, rawScore: 70, date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) },
        { tScore: currentResult.tScore, rawScore: currentResult.rawScore, date: new Date() }
    ];

    return mockHistory;
}

function displayPerformanceMessage(results) {
    const container = document.getElementById('performance-message');
    let message = '';
    let className = '';
    
    if (results.tScore >= 70) {
        message = `🌟 Excellent performance! Your T-Score of ${results.tScore} places you in the top tier. You've demonstrated exceptional knowledge across multiple difficulty levels.`;
        className = 'excellent';
    } else if (results.tScore >= 60) {
        message = `🎯 Great job! Your T-Score of ${results.tScore} is above average. You're in the qualifying range and show strong understanding of the material.`;
        className = 'good';
    } else if (results.tScore >= 50) {
        message = `📈 Good effort! Your T-Score of ${results.tScore} is around average. With some practice, you can improve your performance significantly.`;
        className = 'average';
    } else {
        message = `💪 Keep practicing! Your T-Score of ${results.tScore} shows room for improvement. Focus on the areas where you struggled and try again.`;
        className = 'below-average';
    }
    
    container.innerHTML = `<div class="message ${className}">${message}</div>`;
}

function displayDetailedStats(results) {
    // Calculate average response time
    const totalTime = results.answers.reduce((sum, answer) => sum + (answer.timeTaken || 0), 0);
    const avgTime = results.answers.length > 0 ? Math.round(totalTime / results.answers.length / 1000) : 0;
    document.getElementById('avg-response-time').textContent = `${avgTime}s`;
    
    // Count hints used (if hint mode was enabled)
    const hintsUsed = results.hintModeUsed ? 'Enabled' : 'Disabled';
    document.getElementById('hints-used').textContent = hintsUsed;
    
    // Calculate difficulty changes
    let difficultyChanges = 0;
    for (let i = 1; i < results.answers.length; i++) {
        if (results.answers[i].difficulty !== results.answers[i-1].difficulty) {
            difficultyChanges++;
        }
    }
    document.getElementById('difficulty-changes').textContent = difficultyChanges;
    
    // Calculate best streak
    let currentStreak = 0;
    let bestStreak = 0;
    results.answers.forEach(answer => {
        if (answer.isCorrect) {
            currentStreak++;
            bestStreak = Math.max(bestStreak, currentStreak);
        } else {
            currentStreak = 0;
        }
    });
    document.getElementById('best-streak').textContent = bestStreak;
}

function setupEventListeners() {
    // Retake quiz button
    document.getElementById('retake-quiz').addEventListener('click', () => {
        localStorage.removeItem('quizResults');
        window.location.href = 'index.html';
    });
    
    // View leaderboard button (placeholder)
    document.getElementById('view-leaderboard').addEventListener('click', () => {
        alert('Leaderboard feature coming soon!');
    });
    
    // Share results button
    document.getElementById('share-results').addEventListener('click', () => {
        const results = JSON.parse(localStorage.getItem('quizResults'));
        const shareText = `I just scored ${results.rawScore}% (T-Score: ${results.tScore}) on the Adaptive Learning Quiz! 🎯`;
        
        if (navigator.share) {
            navigator.share({
                title: 'My Quiz Results',
                text: shareText,
                url: window.location.origin
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                alert('Results copied to clipboard!');
            }).catch(() => {
                alert('Share text: ' + shareText);
            });
        }
    });
    
    // Toggle detailed stats
    document.getElementById('toggle-details').addEventListener('click', () => {
        const content = document.getElementById('detailed-content');
        const button = document.getElementById('toggle-details');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            button.textContent = 'Hide Detailed Statistics';
        } else {
            content.style.display = 'none';
            button.textContent = 'Show Detailed Statistics';
        }
    });
}

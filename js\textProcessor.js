// Text Processing Utilities for LaTeX and Chemistry Notation
class TextProcessor {
    constructor() {
        this.chemicalFormulas = new Set([
            'H2O', 'CO2', 'NaCl', 'CaCO3', 'H2SO4', 'HCl', 'NH3', 'CH4', 'C2H6', 'C6H12O6',
            'O2', 'N2', 'Cl2', 'H2', 'He', 'Ne', 'Ar', 'Kr', 'Xe', 'Rn',
            'NaOH', 'KOH', 'Ca(OH)2', 'Mg(OH)2', 'Al(OH)3', 'Fe(OH)3',
            'HNO3', 'H3PO4', 'H2CO3', 'CH3COOH', 'C2H5OH', 'C6H6',
            'CuSO4', 'FeSO4', 'ZnSO4', 'MgSO4', 'Al2(SO4)3',
            'AgNO3', 'Pb(NO3)2', 'Cu(NO3)2', 'Fe(NO3)3'
        ]);
    }

    // Process text for LaTeX and chemistry notation
    processText(text) {
        if (!text) return text;
        
        // First process chemistry notation
        let processedText = this.processChemistryNotation(text);
        
        // Then process any remaining LaTeX
        processedText = this.processLatex(processedText);
        
        return processedText;
    }

    // Process chemistry notation automatically
    processChemistryNotation(text) {
        // Pattern to match chemical formulas with numbers
        // Matches: H2O, CaCO3, Al2(SO4)3, etc.
        const chemicalPattern = /\b([A-Z][a-z]?)(\d+)|(\()([A-Z][a-z]?\d*)+(\))(\d+)/g;
        
        let processedText = text;
        
        // Process simple chemical formulas (like H2O, CO2)
        processedText = processedText.replace(/\b([A-Z][a-z]?)(\d+)/g, (match, element, number) => {
            // Check if this looks like a chemical formula
            if (this.isLikelyChemicalFormula(match)) {
                return `${element}<sub>${number}</sub>`;
            }
            return match;
        });
        
        // Process complex chemical formulas with parentheses (like Ca(OH)2)
        processedText = processedText.replace(/(\()([A-Z][a-z]?\d*)+(\))(\d+)/g, (match, openParen, middle, closeParen, number) => {
            const processedMiddle = middle.replace(/([A-Z][a-z]?)(\d+)/g, '$1<sub>$2</sub>');
            return `${openParen}${processedMiddle}${closeParen}<sub>${number}</sub>`;
        });
        
        // Process charges (like Ca2+, Cl-)
        processedText = processedText.replace(/([A-Z][a-z]?)(\d*)([+-])/g, (match, element, number, charge) => {
            if (number) {
                return `${element}<sup>${number}${charge}</sup>`;
            } else {
                return `${element}<sup>${charge}</sup>`;
            }
        });
        
        // Process isotopes (like C-14, U-235)
        processedText = processedText.replace(/([A-Z][a-z]?)-(\d+)/g, '<sup>$2</sup>$1');
        
        return processedText;
    }

    // Check if a string is likely a chemical formula
    isLikelyChemicalFormula(text) {
        // Check against known chemical formulas
        if (this.chemicalFormulas.has(text)) {
            return true;
        }
        
        // Check pattern: starts with capital letter, may have lowercase, ends with number
        const chemicalPattern = /^[A-Z][a-z]?\d+$/;
        if (chemicalPattern.test(text)) {
            return true;
        }
        
        // Check for common chemical elements
        const commonElements = ['H', 'He', 'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne', 
                               'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar', 'K', 'Ca',
                               'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn',
                               'Ga', 'Ge', 'As', 'Se', 'Br', 'Kr', 'Rb', 'Sr', 'Y', 'Zr',
                               'Nb', 'Mo', 'Tc', 'Ru', 'Rh', 'Pd', 'Ag', 'Cd', 'In', 'Sn',
                               'Sb', 'Te', 'I', 'Xe', 'Cs', 'Ba', 'La', 'Ce', 'Pr', 'Nd',
                               'Pm', 'Sm', 'Eu', 'Gd', 'Tb', 'Dy', 'Ho', 'Er', 'Tm', 'Yb',
                               'Lu', 'Hf', 'Ta', 'W', 'Re', 'Os', 'Ir', 'Pt', 'Au', 'Hg',
                               'Tl', 'Pb', 'Bi', 'Po', 'At', 'Rn', 'Fr', 'Ra', 'Ac', 'Th',
                               'Pa', 'U', 'Np', 'Pu', 'Am', 'Cm', 'Bk', 'Cf', 'Es', 'Fm',
                               'Md', 'No', 'Lr', 'Rf', 'Db', 'Sg', 'Bh', 'Hs', 'Mt', 'Ds',
                               'Rg', 'Cn', 'Nh', 'Fl', 'Mc', 'Lv', 'Ts', 'Og'];
        
        const element = text.match(/^([A-Z][a-z]?)/);
        if (element && commonElements.includes(element[1])) {
            return true;
        }
        
        return false;
    }

    // Process LaTeX notation
    processLatex(text) {
        // Don't process if already contains HTML tags (already processed)
        if (text.includes('<sub>') || text.includes('<sup>') || text.includes('$')) {
            return text;
        }
        
        // Simple LaTeX patterns that can be converted to HTML
        let processedText = text;
        
        // Convert simple superscripts: x^2 -> x<sup>2</sup>
        processedText = processedText.replace(/([a-zA-Z0-9])(\^)(\{([^}]+)\}|(\d+|[a-zA-Z]))/g, (match, base, caret, group, braced, simple) => {
            const exponent = braced || simple;
            return `${base}<sup>${exponent}</sup>`;
        });
        
        // Convert simple subscripts: x_2 -> x<sub>2</sub>
        processedText = processedText.replace(/([a-zA-Z0-9])(_)(\{([^}]+)\}|(\d+|[a-zA-Z]))/g, (match, base, underscore, group, braced, simple) => {
            const subscript = braced || simple;
            return `${base}<sub>${subscript}</sub>`;
        });
        
        // Convert fractions: \frac{a}{b} -> a/b (simple conversion)
        processedText = processedText.replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '($1)/($2)');
        
        // Convert square roots: \sqrt{x} -> √x
        processedText = processedText.replace(/\\sqrt\{([^}]+)\}/g, '√($1)');
        
        // Convert common Greek letters
        const greekLetters = {
            '\\alpha': 'α', '\\beta': 'β', '\\gamma': 'γ', '\\delta': 'δ',
            '\\epsilon': 'ε', '\\zeta': 'ζ', '\\eta': 'η', '\\theta': 'θ',
            '\\iota': 'ι', '\\kappa': 'κ', '\\lambda': 'λ', '\\mu': 'μ',
            '\\nu': 'ν', '\\xi': 'ξ', '\\omicron': 'ο', '\\pi': 'π',
            '\\rho': 'ρ', '\\sigma': 'σ', '\\tau': 'τ', '\\upsilon': 'υ',
            '\\phi': 'φ', '\\chi': 'χ', '\\psi': 'ψ', '\\omega': 'ω',
            '\\Alpha': 'Α', '\\Beta': 'Β', '\\Gamma': 'Γ', '\\Delta': 'Δ',
            '\\Epsilon': 'Ε', '\\Zeta': 'Ζ', '\\Eta': 'Η', '\\Theta': 'Θ',
            '\\Iota': 'Ι', '\\Kappa': 'Κ', '\\Lambda': 'Λ', '\\Mu': 'Μ',
            '\\Nu': 'Ν', '\\Xi': 'Ξ', '\\Omicron': 'Ο', '\\Pi': 'Π',
            '\\Rho': 'Ρ', '\\Sigma': 'Σ', '\\Tau': 'Τ', '\\Upsilon': 'Υ',
            '\\Phi': 'Φ', '\\Chi': 'Χ', '\\Psi': 'Ψ', '\\Omega': 'Ω'
        };
        
        Object.entries(greekLetters).forEach(([latex, unicode]) => {
            processedText = processedText.replace(new RegExp(latex.replace('\\', '\\\\'), 'g'), unicode);
        });
        
        // Convert common mathematical symbols
        const mathSymbols = {
            '\\pm': '±', '\\mp': '∓', '\\times': '×', '\\div': '÷',
            '\\neq': '≠', '\\leq': '≤', '\\geq': '≥', '\\approx': '≈',
            '\\equiv': '≡', '\\propto': '∝', '\\infty': '∞',
            '\\partial': '∂', '\\nabla': '∇', '\\sum': '∑', '\\prod': '∏',
            '\\int': '∫', '\\oint': '∮', '\\angle': '∠', '\\degree': '°'
        };
        
        Object.entries(mathSymbols).forEach(([latex, unicode]) => {
            processedText = processedText.replace(new RegExp(latex.replace('\\', '\\\\'), 'g'), unicode);
        });
        
        return processedText;
    }

    // Process text and trigger MathJax re-rendering if needed
    async processAndRender(text, element) {
        const processedText = this.processText(text);
        
        if (element) {
            element.innerHTML = processedText;
            
            // If MathJax is available and text contains LaTeX, re-render
            if (window.MathJax && (text.includes('$') || text.includes('\\('))) {
                try {
                    await window.MathJax.typesetPromise([element]);
                } catch (error) {
                    console.warn('MathJax rendering error:', error);
                }
            }
        }
        
        return processedText;
    }

    // Utility method to check if text contains LaTeX
    containsLatex(text) {
        const latexPatterns = [
            /\$.*?\$/,  // Inline math
            /\\\(.*?\\\)/,  // Inline math alternative
            /\$\$.*?\$\$/,  // Display math
            /\\\[.*?\\\]/,  // Display math alternative
            /\\[a-zA-Z]+/,  // LaTeX commands
            /\^[{]?[^}]*[}]?/,  // Superscripts
            /_[{]?[^}]*[}]?/   // Subscripts
        ];
        
        return latexPatterns.some(pattern => pattern.test(text));
    }

    // Utility method to check if text contains chemistry notation
    containsChemistry(text) {
        const chemPatterns = [
            /\b[A-Z][a-z]?\d+/,  // Simple formulas like H2O
            /\([A-Z][a-z]?\d*\)\d+/,  // Complex formulas like Ca(OH)2
            /[A-Z][a-z]?\d*[+-]/,  // Ions like Ca2+
            /[A-Z][a-z]?-\d+/   // Isotopes like C-14
        ];
        
        return chemPatterns.some(pattern => pattern.test(text));
    }
}

// Create global instance
const textProcessor = new TextProcessor();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TextProcessor;
}
